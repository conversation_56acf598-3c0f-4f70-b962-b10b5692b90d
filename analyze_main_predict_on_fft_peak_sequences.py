#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分析main_predict_improved.py模型在FFT选定高峰时刻序列的预测性能
每个车站：高峰时刻10个时间步×5天=50个预测值
总计：276个车站×50=13,800个预测值

Author: 008 - AI Algorithm Engineer for Subway Flow Prediction
"""

import numpy as np
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import os

def load_main_predict_results():
    """加载main_predict的预测结果"""
    print("正在加载main_predict的预测结果...")
    
    # 加载预测值 (276个车站, N个时间步)
    predictions = np.loadtxt('result/improved_ALL_276_stations_predictions.txt')
    print(f"预测值形状: {predictions.shape}")
    
    # 加载真实值 (276个车站, N个时间步)
    true_values = np.loadtxt('result/improved_ALL_276_stations_original.txt')
    print(f"真实值形状: {true_values.shape}")
    
    return predictions, true_values

def load_fft_peak_times_and_mapping():
    """加载FFT高峰时刻信息和时间映射"""
    print("正在加载FFT高峰时刻信息...")
    
    # 加载原始数据以了解数据结构
    raw_data = np.loadtxt('./data/in_15min.csv', delimiter=",")
    if raw_data.shape[0] != 276:
        raw_data = raw_data.T
    
    print(f"原始数据形状: {raw_data.shape}")
    
    # 加载高峰时刻信息
    peak_times = np.loadtxt('./data/continuous_peak_flow_dataset_peak_times.csv', delimiter=',', dtype=int)
    print(f"高峰时刻数据形状: {peak_times.shape}")
    
    # 数据集参数
    total_days = 25
    time_steps_per_day = 72
    test_days = 5  # 最后5天为测试集
    train_val_days = 20  # 前20天为训练+验证集
    sequence_length = 10  # 高峰时刻左右10个时间步
    
    print(f"数据集信息:")
    print(f"- 总天数: {total_days}")
    print(f"- 每天时间步: {time_steps_per_day}")
    print(f"- 训练+验证天数: {train_val_days}")
    print(f"- 测试天数: {test_days}")
    print(f"- 高峰序列长度: {sequence_length}")
    
    return peak_times, raw_data, train_val_days, test_days, time_steps_per_day, sequence_length

def map_fft_peak_sequences_to_main_predict_test_set(peak_times, train_val_days, test_days, 
                                                   time_steps_per_day, sequence_length):
    """将FFT高峰时刻序列映射到main_predict的测试集时间索引"""
    print("正在映射FFT高峰时刻序列到main_predict测试集...")
    
    # main_predict的测试集对应原始数据的最后5天
    test_start_day = train_val_days  # 第21天开始
    
    fft_peak_sequence_indices = []
    
    for station_idx in range(276):
        station_sequence_indices = []
        peak_time = peak_times[station_idx]
        
        # 对测试集的每一天，找到对应的高峰时刻序列
        for test_day in range(test_days):
            actual_day = test_start_day + test_day  # 第21-25天
            
            # 计算该天该车站高峰时刻在原始数据中的绝对索引
            day_start_index = actual_day * time_steps_per_day
            peak_center_index = day_start_index + peak_time
            
            # 计算高峰时刻左右10个时间步的索引范围
            # 以高峰时刻为中心，前4个+高峰时刻+后5个 = 10个时间步
            sequence_start = peak_center_index - 4
            sequence_end = peak_center_index + 6  # 不包含end，所以是+6
            
            # 确保序列在有效范围内
            sequence_start = max(sequence_start, day_start_index)
            sequence_end = min(sequence_end, day_start_index + time_steps_per_day)
            
            # 转换为main_predict测试集中的相对索引
            test_set_start_index = train_val_days * time_steps_per_day
            
            day_sequence_indices = []
            for abs_idx in range(sequence_start, sequence_end):
                relative_idx = abs_idx - test_set_start_index
                if 0 <= relative_idx < 359:  # 确保在测试集范围内
                    day_sequence_indices.append(relative_idx)
            
            # 如果序列长度不足10，进行填充
            while len(day_sequence_indices) < sequence_length:
                if len(day_sequence_indices) > 0:
                    # 用最后一个有效索引填充
                    day_sequence_indices.append(day_sequence_indices[-1])
                else:
                    # 如果完全没有有效索引，用0填充
                    day_sequence_indices.append(0)
            
            # 如果序列长度超过10，截取前10个
            day_sequence_indices = day_sequence_indices[:sequence_length]
            
            station_sequence_indices.extend(day_sequence_indices)
        
        fft_peak_sequence_indices.append(station_sequence_indices)
    
    print(f"FFT高峰时刻序列映射完成，每个车站在测试集中有{test_days * sequence_length}个时间步")
    
    return fft_peak_sequence_indices

def extract_peak_sequence_predictions(predictions, true_values, fft_peak_sequence_indices):
    """提取高峰时刻序列的预测值和真实值"""
    print("正在提取高峰时刻序列的预测值和真实值...")
    
    peak_sequence_predictions = []
    peak_sequence_true_values = []
    
    for station_idx in range(276):
        station_sequence_indices = fft_peak_sequence_indices[station_idx]
        
        for seq_idx in station_sequence_indices:
            # 确保索引在有效范围内
            if 0 <= seq_idx < predictions.shape[1] and 0 <= seq_idx < true_values.shape[1]:
                peak_sequence_predictions.append(predictions[station_idx, seq_idx])
                peak_sequence_true_values.append(true_values[station_idx, seq_idx])
            else:
                print(f"警告: 车站{station_idx}的序列索引{seq_idx}超出范围")
                # 使用边界值
                if seq_idx < 0:
                    peak_sequence_predictions.append(predictions[station_idx, 0])
                    peak_sequence_true_values.append(true_values[station_idx, 0])
                else:
                    peak_sequence_predictions.append(predictions[station_idx, -1])
                    peak_sequence_true_values.append(true_values[station_idx, -1])
    
    peak_sequence_predictions = np.array(peak_sequence_predictions)
    peak_sequence_true_values = np.array(peak_sequence_true_values)
    
    print(f"提取完成:")
    print(f"- 高峰序列预测值数量: {len(peak_sequence_predictions)}")
    print(f"- 高峰序列真实值数量: {len(peak_sequence_true_values)}")
    print(f"- 预期数量: {276 * 5 * 10} (276个车站 × 5个测试天 × 10个时间步)")
    
    return peak_sequence_predictions, peak_sequence_true_values

def calculate_metrics(predictions, true_values):
    """计算评估指标"""
    print("正在计算评估指标...")
    
    # 计算RMSE
    rmse = np.sqrt(mean_squared_error(true_values, predictions))
    
    # 计算MAE
    mae = mean_absolute_error(true_values, predictions)
    
    # 计算WMAPE
    mask = true_values > 0
    if np.sum(mask) > 0:
        wmape = np.sum(np.abs(predictions[mask] - true_values[mask])) / np.sum(true_values[mask])
    else:
        wmape = 0
    
    # 计算R2
    r2 = r2_score(true_values, predictions)
    
    return rmse, mae, wmape, r2

def calculate_station_level_metrics(predictions, true_values, fft_peak_sequence_indices, peak_times):
    """计算各车站在高峰时刻序列的评估指标"""
    print("正在计算各车站在高峰时刻序列的评估指标...")
    
    station_metrics = []
    
    for station_idx in range(276):
        station_sequence_indices = fft_peak_sequence_indices[station_idx]
        
        station_predictions = []
        station_true_values = []
        
        for seq_idx in station_sequence_indices:
            if 0 <= seq_idx < predictions.shape[1] and 0 <= seq_idx < true_values.shape[1]:
                station_predictions.append(predictions[station_idx, seq_idx])
                station_true_values.append(true_values[station_idx, seq_idx])
            else:
                # 使用边界值
                if seq_idx < 0:
                    station_predictions.append(predictions[station_idx, 0])
                    station_true_values.append(true_values[station_idx, 0])
                else:
                    station_predictions.append(predictions[station_idx, -1])
                    station_true_values.append(true_values[station_idx, -1])
        
        if len(station_predictions) > 0:
            station_predictions = np.array(station_predictions)
            station_true_values = np.array(station_true_values)
            
            # 计算该车站的指标
            station_rmse = np.sqrt(mean_squared_error(station_true_values, station_predictions))
            station_mae = mean_absolute_error(station_true_values, station_predictions)
            
            mask = station_true_values > 0
            if np.sum(mask) > 0:
                station_wmape = np.sum(np.abs(station_predictions[mask] - station_true_values[mask])) / np.sum(station_true_values[mask])
            else:
                station_wmape = 0
            
            station_r2 = r2_score(station_true_values, station_predictions)
            
            station_metrics.append({
                'station_id': station_idx,
                'peak_time': peak_times[station_idx],
                'rmse': station_rmse,
                'mae': station_mae,
                'wmape': station_wmape,
                'r2': station_r2,
                'sequence_count': len(station_predictions)
            })
        else:
            station_metrics.append({
                'station_id': station_idx,
                'peak_time': peak_times[station_idx],
                'rmse': 0,
                'mae': 0,
                'wmape': 0,
                'r2': 0,
                'sequence_count': 0
            })
    
    return station_metrics

def save_results(overall_rmse, overall_mae, overall_wmape, overall_r2, 
                station_metrics, peak_sequence_predictions, peak_sequence_true_values):
    """保存分析结果"""
    if not os.path.exists('result'):
        os.makedirs('result')
    
    # 保存总体结果
    with open('result/main_predict_fft_peak_sequences_analysis.txt', 'w', encoding='utf-8') as f:
        f.write("main_predict_improved.py模型在FFT高峰时刻序列的预测性能分析\n")
        f.write("=" * 80 + "\n\n")
        
        f.write("分析说明:\n")
        f.write("-" * 40 + "\n")
        f.write("- 使用main_predict_improved.py的预测结果文件\n")
        f.write("- 分析在fft_continuous_prediction.py选定的高峰时刻序列的预测性能\n")
        f.write("- 评估范围：测试集(最后5天)的高峰时刻左右10个时间步\n")
        f.write("- 每个车站：10个时间步 × 5天 = 50个预测值\n")
        f.write(f"- 总计：276个车站 × 50 = {len(peak_sequence_predictions)}个预测值\n\n")
        
        f.write("整体评估指标(测试集高峰时刻序列):\n")
        f.write("-" * 40 + "\n")
        f.write(f"RMSE: {overall_rmse:.4f}\n")
        f.write(f"MAE: {overall_mae:.4f}\n")
        f.write(f"WMAPE: {overall_wmape:.4f} ({overall_wmape*100:.2f}%)\n")
        f.write(f"R2: {overall_r2:.4f}\n\n")
        
        f.write("数据统计:\n")
        f.write("-" * 40 + "\n")
        f.write(f"高峰序列预测值范围: [{np.min(peak_sequence_predictions):.2f}, {np.max(peak_sequence_predictions):.2f}]\n")
        f.write(f"高峰序列真实值范围: [{np.min(peak_sequence_true_values):.2f}, {np.max(peak_sequence_true_values):.2f}]\n")
        f.write(f"高峰序列预测值平均: {np.mean(peak_sequence_predictions):.2f}\n")
        f.write(f"高峰序列真实值平均: {np.mean(peak_sequence_true_values):.2f}\n\n")
        
        # 各车站指标统计
        valid_metrics = [m for m in station_metrics if m['sequence_count'] > 0]
        if valid_metrics:
            rmses = [m['rmse'] for m in valid_metrics]
            maes = [m['mae'] for m in valid_metrics]
            wmapes = [m['wmape'] for m in valid_metrics]
            r2s = [m['r2'] for m in valid_metrics]
            
            f.write("各车站高峰序列指标统计:\n")
            f.write("-" * 40 + "\n")
            f.write(f"RMSE - 平均: {np.mean(rmses):.2f}, 标准差: {np.std(rmses):.2f}, 最小: {np.min(rmses):.2f}, 最大: {np.max(rmses):.2f}\n")
            f.write(f"MAE  - 平均: {np.mean(maes):.2f}, 标准差: {np.std(maes):.2f}, 最小: {np.min(maes):.2f}, 最大: {np.max(maes):.2f}\n")
            f.write(f"WMAPE- 平均: {np.mean(wmapes):.4f}, 标准差: {np.std(wmapes):.4f}, 最小: {np.min(wmapes):.4f}, 最大: {np.max(wmapes):.4f}\n")
            f.write(f"R2   - 平均: {np.mean(r2s):.4f}, 标准差: {np.std(r2s):.4f}, 最小: {np.min(r2s):.4f}, 最大: {np.max(r2s):.4f}\n\n")
        
        # 前20个车站的详细指标
        f.write("前20个车站高峰序列详细指标:\n")
        f.write("-" * 80 + "\n")
        f.write("车站ID | 高峰时刻 | RMSE     | MAE      | WMAPE    | R2       | 序列数\n")
        f.write("-" * 80 + "\n")
        
        for i in range(min(20, len(station_metrics))):
            metrics = station_metrics[i]
            peak_time = metrics['peak_time']
            hour = peak_time // 4
            minute = (peak_time % 4) * 15
            
            f.write(f"{metrics['station_id']:6d} | {hour:02d}:{minute:02d}     | {metrics['rmse']:8.2f} | {metrics['mae']:8.2f} | {metrics['wmape']:8.4f} | {metrics['r2']:8.4f} | {metrics['sequence_count']:7d}\n")
        
        f.write("...\n")
        f.write(f"(显示前20个车站，共{len(station_metrics)}个车站的高峰序列数据)\n")
    
    print("分析结果已保存到 result/main_predict_fft_peak_sequences_analysis.txt")

def main():
    """主函数"""
    print("=" * 80)
    print("main_predict_improved.py模型在FFT高峰时刻序列的预测性能分析")
    print("=" * 80)
    
    # 1. 加载main_predict的预测结果
    predictions, true_values = load_main_predict_results()
    
    # 2. 加载FFT高峰时刻信息
    peak_times, raw_data, train_val_days, test_days, time_steps_per_day, sequence_length = load_fft_peak_times_and_mapping()
    
    # 3. 映射FFT高峰时刻序列到main_predict测试集
    fft_peak_sequence_indices = map_fft_peak_sequences_to_main_predict_test_set(
        peak_times, train_val_days, test_days, time_steps_per_day, sequence_length)
    
    # 4. 提取高峰时刻序列的预测值和真实值
    peak_sequence_predictions, peak_sequence_true_values = extract_peak_sequence_predictions(
        predictions, true_values, fft_peak_sequence_indices)
    
    # 5. 计算整体评估指标
    overall_rmse, overall_mae, overall_wmape, overall_r2 = calculate_metrics(
        peak_sequence_predictions, peak_sequence_true_values)
    
    # 6. 计算各车站级别的指标
    station_metrics = calculate_station_level_metrics(
        predictions, true_values, fft_peak_sequence_indices, peak_times)
    
    # 7. 输出结果
    print("\n" + "=" * 80)
    print("main_predict模型在FFT高峰时刻序列的预测性能(测试集):")
    print("-" * 80)
    print(f"高峰序列预测点数: {len(peak_sequence_predictions)} (276个车站 × 5天 × 10时间步)")
    print(f"RMSE: {overall_rmse:.4f}")
    print(f"MAE: {overall_mae:.4f}")
    print(f"WMAPE: {overall_wmape:.4f} ({overall_wmape*100:.2f}%)")
    print(f"R2: {overall_r2:.4f}")
    print("=" * 80)
    
    # 8. 保存详细结果
    save_results(overall_rmse, overall_mae, overall_wmape, overall_r2,
                station_metrics, peak_sequence_predictions, peak_sequence_true_values)
    
    print("\n分析完成！")
    print("这些指标反映了main_predict模型在FFT选定的高峰时刻序列的预测准确性。")

if __name__ == "__main__":
    main()
