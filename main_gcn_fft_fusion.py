import numpy as np
import os
import time
import torch
from torch import nn
import sys

# 添加当前目录到Python路径
if '.' not in sys.path:
    sys.path.append('.')

# 可选导入
try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_AVAILABLE = True
except ImportError:
    print("Warning: TensorBoard not available. Logging will be disabled.")
    TENSORBOARD_AVAILABLE = False

# 导入模块
try:
    from utils.utils import GetLaplacian
    from model.gcn_fft_fusion_model import GCN_FFT_FusionModel
    from utils.earlystopping import EarlyStopping
    from data.get_inflow_only_dataloader import get_inflow_only_dataloader
    print("All modules imported successfully!")
except ImportError as e:
    print(f"Import error: {e}")
    print("Please check if all required files exist in the correct directories.")
    exit(1)


def main():
    """主训练函数"""
    # 设备配置
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # 模型参数配置
    epoch_num = 1000
    lr = 0.001
    time_interval = 15
    time_lag = 10
    tg_in_one_day = 72
    forecast_day_number = 5
    pre_len = 1
    batch_size = 32
    station_num = 276
    peak_time_steps = 10  # FFT处理的高峰期时间步数
    model_type = 'gcn_fft_fusion'

    # 创建保存目录
    TIMESTAMP = str(time.strftime("%Y_%m_%d_%H_%M_%S"))
    save_dir = './save_model/' + model_type + '_' + TIMESTAMP
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    print("=" * 80)
    print("GCN + FFT Fusion Metro Flow Prediction System")
    print("基于GCN和FFT融合的地铁客流预测系统")
    print("=" * 80)
    print(f"Model Configuration:")
    print(f"  - Model Type: {model_type}")
    print(f"  - Time Lag: {time_lag}")
    print(f"  - Prediction Length: {pre_len}")
    print(f"  - Station Number: {station_num}")
    print(f"  - Peak Time Steps: {peak_time_steps}")
    print(f"  - Batch Size: {batch_size}")
    print(f"  - Learning Rate: {lr}")
    print(f"  - Max Epochs: {epoch_num}")
    print(f"  - Save Directory: {save_dir}")
    print("=" * 80)
    print("Architecture Details:")
    print("  - GCN: 处理实时客流、日客流、周客流预测")
    print("  - FFT: 专门处理高峰期10个时间步的频域预测")
    print("  - Fusion: 自适应权重融合GCN和FFT预测结果")
    print("=" * 80)

    # 加载数据 - 只使用inflow数据
    print("Loading data...")
    inflow_data_loader_train, inflow_data_loader_val, inflow_data_loader_test, max_inflow, min_inflow = \
        get_inflow_only_dataloader(
            time_interval=time_interval, 
            time_lag=time_lag, 
            tg_in_one_day=tg_in_one_day, 
            forecast_day_number=forecast_day_number, 
            pre_len=pre_len, 
            batch_size=batch_size
        )

    print(f"Data normalization - max_inflow: {max_inflow:.2f}, min_inflow: {min_inflow:.2f}")

    # 加载邻接矩阵
    print("Loading adjacency matrix...")
    adjacency = np.loadtxt('./data/adjacency.csv', delimiter=",")
    adjacency = torch.tensor(GetLaplacian(adjacency).get_normalized_adj(station_num)).type(torch.float32).to(device)
    print(f"Adjacency matrix shape: {adjacency.shape}")

    # 创建融合模型
    print("Creating GCN+FFT Fusion model...")
    model = GCN_FFT_FusionModel(time_lag, pre_len, station_num, device, peak_time_steps)
    model = model.to(device)

    # 打印模型信息
    model_info = model.get_model_info()
    print("\nModel Information:")
    for key, value in model_info.items():
        print(f"  {key}: {value}")

    # 权重初始化
    def weights_init(m):
        classname = m.__class__.__name__
        if classname.find('Conv2d') != -1:
            nn.init.xavier_normal_(m.weight.data)
            nn.init.constant_(m.bias.data, 0.0)
        elif classname.find('Linear') != -1:
            nn.init.xavier_normal_(m.weight.data)
            if m.bias is not None:
                nn.init.constant_(m.bias.data, 0.0)

    model.apply(weights_init)

    # 优化器和损失函数
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    mse_loss = torch.nn.MSELoss().to(device)

    # TensorBoard
    if TENSORBOARD_AVAILABLE:
        writer = SummaryWriter(log_dir=f'./runs/{model_type}_{TIMESTAMP}')
    else:
        writer = None

    # 早停机制
    early_stopping = EarlyStopping(patience=100, verbose=True)

    # 训练开始
    print("\n" + "=" * 80)
    print("Starting Training...")
    print("=" * 80)

    global_start_time = time.time()
    temp_time = time.time()

    best_val_loss = float('inf')
    train_losses = []
    val_losses = []

    for epoch in range(epoch_num):
        # 训练阶段
        model.train()
        train_loss = 0
        train_batches = 0
        
        for i_batch, (train_inflow_X, train_inflow_Y) in enumerate(inflow_data_loader_train):
            train_inflow_X = train_inflow_X.type(torch.float32).to(device)
            train_inflow_Y = train_inflow_Y.type(torch.float32).to(device)
            
            # 前向传播
            prediction = model(train_inflow_X, adjacency)
            loss = mse_loss(prediction, train_inflow_Y)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_batches += 1
        
        # 验证阶段
        model.eval()
        val_loss = 0
        val_batches = 0
        
        with torch.no_grad():
            for i_batch, (val_inflow_X, val_inflow_Y) in enumerate(inflow_data_loader_val):
                val_inflow_X = val_inflow_X.type(torch.float32).to(device)
                val_inflow_Y = val_inflow_Y.type(torch.float32).to(device)
                
                prediction = model(val_inflow_X, adjacency)
                loss = mse_loss(prediction, val_inflow_Y)
                
                val_loss += loss.item()
                val_batches += 1
        
        # 计算平均损失
        avg_train_loss = train_loss / train_batches
        avg_val_loss = val_loss / val_batches
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        
        # 记录到TensorBoard
        if writer is not None:
            writer.add_scalar("Loss/Train", avg_train_loss, epoch)
            writer.add_scalar("Loss/Validation", avg_val_loss, epoch)
        
        # 打印训练信息
        if epoch % 10 == 0 or epoch < 10:
            print(f'Epoch {epoch:4d}: Train Loss = {avg_train_loss:.6f}, Val Loss = {avg_val_loss:.6f}')
        
        # 早停检查
        if epoch > 0:
            model_dict = model.state_dict()
            early_stopping(avg_val_loss, model_dict, model, epoch, save_dir)
            if early_stopping.early_stop:
                print(f"Early stopping at epoch {epoch}")
                break
        
        # 每50个epoch打印时间信息
        if epoch % 50 == 0 and epoch > 0:
            elapsed_time = time.time() - temp_time
            print(f"Time for last 50 epochs: {elapsed_time:.2f} seconds")
            temp_time = time.time()

    # 训练结束
    global_end_time = time.time() - global_start_time
    print("\n" + "=" * 80)
    print("Training Completed!")
    print(f"Total training time: {global_end_time:.2f} seconds")
    print(f"Best validation loss: {early_stopping.best_score:.6f}")
    print("=" * 80)

    # 保存训练时间
    train_time_file = f'result/{model_type}_lr_{lr}_batch_size_{batch_size}_Train_time_ALL.txt'
    os.makedirs('result', exist_ok=True)
    np.savetxt(train_time_file, [global_end_time])

    # 关闭TensorBoard writer
    if writer is not None:
        writer.close()

    print(f"Training completed! Model saved in: {save_dir}")
    print(f"Training time saved in: {train_time_file}")

    print("\nGCN+FFT Fusion model training completed successfully!")
    print("Architecture Summary:")
    print("  ✅ GCN: 多尺度客流预测 (实时/日/周)")
    print("  ✅ FFT: 高峰期频域预测 (10个时间步)")
    print("  ✅ Fusion: 自适应权重融合")
    print("You can now run predict_gcn_fft_fusion.py to make predictions.")


if __name__ == "__main__":
    main()
