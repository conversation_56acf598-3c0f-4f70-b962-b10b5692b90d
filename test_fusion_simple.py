"""
简化的融合模型测试
"""
import torch
import numpy as np

def test_fusion_model_simple():
    """简化的融合模型测试"""
    print("Testing GCN+FFT Fusion Model (Simplified)...")
    
    try:
        from model.gcn_fft_fusion_model import GCN_FFT_FusionModel
        
        device = torch.device('cpu')
        time_lag, pre_len, station_num, peak_time_steps = 10, 1, 276, 10
        
        print(f"Model parameters: time_lag={time_lag}, pre_len={pre_len}, station_num={station_num}, peak_time_steps={peak_time_steps}")
        
        # 创建模型
        model = GCN_FFT_FusionModel(time_lag, pre_len, station_num, device, peak_time_steps)
        
        # 测试数据
        batch_size = 2
        test_inflow = torch.randn(batch_size, station_num, time_lag * 3)
        test_adj = torch.randn(station_num, station_num)
        
        print(f"Input shapes: inflow={test_inflow.shape}, adj={test_adj.shape}")
        
        # 测试各个组件
        with torch.no_grad():
            # 1. 测试GCN预测器
            gcn_output = model.gcn_predictor(test_inflow, test_adj)
            print(f"✓ GCN output shape: {gcn_output.shape}")
            
            # 2. 测试高峰期检测
            peak_sequences = model.detect_peak_periods(test_inflow)
            print(f"✓ Peak sequences shape: {peak_sequences.shape}")
            
            # 3. 测试FFT预测器
            fft_output = model.fft_predictor(peak_sequences)
            print(f"✓ FFT output shape: {fft_output.shape}")
            
            # 4. 测试完整的融合模型
            fusion_output = model(test_inflow, test_adj)
            print(f"✓ Fusion output shape: {fusion_output.shape}")
            
            # 验证输出维度
            expected_shape = (batch_size, station_num, pre_len)
            if fusion_output.shape == expected_shape:
                print(f"✓ Output shape correct: {fusion_output.shape}")
                return True
            else:
                print(f"✗ Output shape incorrect: expected {expected_shape}, got {fusion_output.shape}")
                return False
                
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fft_predictor_dimensions():
    """测试FFT预测器的维度处理"""
    print("\nTesting FFT Predictor Dimensions...")
    
    try:
        from model.gcn_fft_fusion_model import FFTPeakPredictor
        
        device = torch.device('cpu')
        time_steps = 10
        predictor = FFTPeakPredictor(time_steps, device)
        
        # 测试不同的输入维度
        test_cases = [
            (2, 276, 10),  # 标准情况
            (1, 276, 10),  # 单个批次
            (4, 276, 10),  # 更大批次
        ]
        
        for batch_size, station_num, ts in test_cases:
            test_data = torch.randn(batch_size, station_num, ts)
            
            with torch.no_grad():
                # 测试FFT特征提取
                fft_features = predictor.extract_fft_features(test_data)
                print(f"  ✓ FFT features {test_data.shape} -> {fft_features.shape}")
                
                # 测试完整预测
                output = predictor(test_data)
                print(f"  ✓ FFT prediction {test_data.shape} -> {output.shape}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ FFT dimension test failed: {e}")
        return False

def test_dimension_adjustment():
    """测试维度调整逻辑"""
    print("\nTesting Dimension Adjustment...")
    
    try:
        from model.gcn_fft_fusion_model import GCN_FFT_FusionModel
        
        device = torch.device('cpu')
        
        # 测试不同的参数组合
        test_configs = [
            (10, 1, 10),  # time_lag=10, pre_len=1, peak_time_steps=10
            (5, 1, 10),   # time_lag=5, pre_len=1, peak_time_steps=10
            (10, 2, 10),  # time_lag=10, pre_len=2, peak_time_steps=10
        ]
        
        for time_lag, pre_len, peak_time_steps in test_configs:
            print(f"  Testing config: time_lag={time_lag}, pre_len={pre_len}, peak_time_steps={peak_time_steps}")
            
            model = GCN_FFT_FusionModel(time_lag, pre_len, 276, device, peak_time_steps)
            
            batch_size = 2
            test_inflow = torch.randn(batch_size, 276, time_lag * 3)
            test_adj = torch.randn(276, 276)
            
            with torch.no_grad():
                output = model(test_inflow, test_adj)
                expected_shape = (batch_size, 276, pre_len)
                
                if output.shape == expected_shape:
                    print(f"    ✓ Output shape correct: {output.shape}")
                else:
                    print(f"    ✗ Output shape incorrect: expected {expected_shape}, got {output.shape}")
                    return False
        
        return True
        
    except Exception as e:
        print(f"  ✗ Dimension adjustment test failed: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("GCN+FFT Fusion Model - Simplified Test")
    print("=" * 60)
    
    tests = [
        ("Fusion Model Basic", test_fusion_model_simple),
        ("FFT Predictor Dimensions", test_fft_predictor_dimensions),
        ("Dimension Adjustment", test_dimension_adjustment),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 40)
        result = test_func()
        results.append((test_name, result))
    
    # 总结
    print("\n" + "=" * 60)
    print("Test Results Summary:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"  {test_name:<25}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All simplified tests passed!")
        print("The GCN+FFT Fusion model core functionality is working correctly.")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
