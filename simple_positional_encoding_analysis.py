#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版位置编码分析 - 专注于数值计算和图片保存

Author: 008 - AI Algorithm Engineer for Subway Flow Prediction
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import math
import os

# 设置matplotlib后端，避免显示问题
import matplotlib
matplotlib.use('Agg')

class PositionalEncoding(nn.Module):
    """位置编码模块"""
    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        self.register_buffer('pe', pe)

    def forward(self, x):
        x = x + self.pe[:, :x.size(1), :]
        return x

def analyze_and_visualize():
    """分析位置编码并生成可视化"""
    print("开始位置编码分析...")
    
    # 工程配置
    d_model = 64
    seq_len = 3  # 工程中的序列长度
    
    # 创建位置编码
    pos_encoder = PositionalEncoding(d_model)
    
    # 获取前3个位置的编码
    pe_matrix = pos_encoder.pe.squeeze(0)[:seq_len, :].numpy()
    
    print(f"位置编码矩阵形状: {pe_matrix.shape}")
    print(f"d_model: {d_model}, seq_len: {seq_len}")
    
    # 显示具体数值
    print("\n工程中3个位置的编码值 (前10个维度):")
    print("=" * 60)
    for pos in range(seq_len):
        position_name = ['Week', 'Day', 'Hour'][pos]
        print(f"位置 {pos} ({position_name}):")
        print(f"前10维: {pe_matrix[pos, :10]}")
        print()
    
    # 创建可视化图
    create_visualizations(pe_matrix, d_model, seq_len)
    
    # 演示位置编码的相加过程
    demonstrate_addition(pos_encoder, d_model, seq_len)
    
    # 保存详细分析
    save_analysis(pe_matrix, d_model, seq_len)

def create_visualizations(pe_matrix, d_model, seq_len):
    """创建位置编码可视化图"""
    print("正在创建可视化图...")
    
    # 确保result目录存在
    if not os.path.exists('result'):
        os.makedirs('result')
    
    # 图1: 位置编码热力图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 子图1: 3个位置的完整编码
    ax1 = axes[0, 0]
    im1 = ax1.imshow(pe_matrix.T, cmap='RdBu', aspect='auto')
    ax1.set_title('Positional Encoding for 3 Positions\n(Week, Day, Hour)', fontsize=12, fontweight='bold')
    ax1.set_xlabel('Position Index', fontsize=10)
    ax1.set_ylabel('Dimension Index', fontsize=10)
    ax1.set_xticks([0, 1, 2])
    ax1.set_xticklabels(['Week', 'Day', 'Hour'])
    ax1.set_yticks(range(0, d_model, 8))
    plt.colorbar(im1, ax=ax1, shrink=0.8)
    
    # 子图2: 前16个维度的详细视图
    ax2 = axes[0, 1]
    im2 = ax2.imshow(pe_matrix[:, :16].T, cmap='RdBu', aspect='auto')
    ax2.set_title('First 16 Dimensions Detail', fontsize=12, fontweight='bold')
    ax2.set_xlabel('Position Index', fontsize=10)
    ax2.set_ylabel('Dimension Index', fontsize=10)
    ax2.set_xticks([0, 1, 2])
    ax2.set_xticklabels(['Week', 'Day', 'Hour'])
    ax2.set_yticks(range(0, 16, 2))
    plt.colorbar(im2, ax=ax2, shrink=0.8)
    
    # 子图3: 波形图 - 前8个维度
    ax3 = axes[1, 0]
    x_pos = range(seq_len)
    for dim in range(0, 8, 2):
        ax3.plot(x_pos, pe_matrix[:, dim], 'o-', label=f'Dim {dim} (sin)', linewidth=2, markersize=6)
        ax3.plot(x_pos, pe_matrix[:, dim+1], 's--', label=f'Dim {dim+1} (cos)', linewidth=2, markersize=6)
    
    ax3.set_title('Positional Encoding Values\n(First 8 Dimensions)', fontsize=12, fontweight='bold')
    ax3.set_xlabel('Position (Week, Day, Hour)', fontsize=10)
    ax3.set_ylabel('Encoding Value', fontsize=10)
    ax3.set_xticks([0, 1, 2])
    ax3.set_xticklabels(['Week', 'Day', 'Hour'])
    ax3.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    ax3.grid(True, alpha=0.3)
    
    # 子图4: 数值表格
    ax4 = axes[1, 1]
    ax4.axis('off')
    
    # 创建数值表格
    table_data = []
    for pos in range(seq_len):
        row = [f'Pos {pos}'] + [f'{pe_matrix[pos, dim]:.3f}' for dim in range(8)]
        table_data.append(row)
    
    col_labels = ['Position'] + [f'Dim {i}' for i in range(8)]
    table = ax4.table(cellText=table_data, colLabels=col_labels, 
                     cellLoc='center', loc='center', bbox=[0, 0, 1, 1])
    table.auto_set_font_size(False)
    table.set_fontsize(9)
    table.scale(1, 2)
    ax4.set_title('Positional Encoding Values\n(First 8 Dimensions)', fontsize=12, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('result/positional_encoding_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("位置编码分析图已保存到 result/positional_encoding_analysis.png")

def demonstrate_addition(pos_encoder, d_model, seq_len):
    """演示位置编码与输入的相加"""
    print("演示位置编码相加过程...")
    
    # 创建模拟输入
    torch.manual_seed(42)
    batch_size = 1
    input_data = torch.randn(batch_size, seq_len, d_model)
    
    # 应用位置编码
    encoded_data = pos_encoder(input_data)
    
    # 获取位置编码值
    pe_values = pos_encoder.pe[0, :seq_len, :].numpy()
    
    print(f"\n位置编码相加演示:")
    print("=" * 60)
    print(f"输入形状: {input_data.shape}")
    print(f"输出形状: {encoded_data.shape}")
    
    for pos in range(seq_len):
        position_name = ['Week', 'Day', 'Hour'][pos]
        print(f"\n位置 {pos} ({position_name}) - 前5个维度:")
        print(f"原始输入: {input_data[0, pos, :5].numpy()}")
        print(f"位置编码: {pe_values[pos, :5]}")
        print(f"相加结果: {encoded_data[0, pos, :5].numpy()}")
    
    # 创建相加过程的可视化
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # 原始输入
    ax1 = axes[0]
    im1 = ax1.imshow(input_data[0].T.numpy(), cmap='viridis', aspect='auto')
    ax1.set_title('Original Input\n(Random Features)', fontsize=12, fontweight='bold')
    ax1.set_xlabel('Position', fontsize=10)
    ax1.set_ylabel('Dimension Index', fontsize=10)
    ax1.set_xticks([0, 1, 2])
    ax1.set_xticklabels(['Week', 'Day', 'Hour'])
    plt.colorbar(im1, ax=ax1, shrink=0.8)
    
    # 位置编码
    ax2 = axes[1]
    im2 = ax2.imshow(pe_values.T, cmap='RdBu', aspect='auto')
    ax2.set_title('Positional Encoding\n(Added to Input)', fontsize=12, fontweight='bold')
    ax2.set_xlabel('Position', fontsize=10)
    ax2.set_ylabel('Dimension Index', fontsize=10)
    ax2.set_xticks([0, 1, 2])
    ax2.set_xticklabels(['Week', 'Day', 'Hour'])
    plt.colorbar(im2, ax=ax2, shrink=0.8)
    
    # 相加结果
    ax3 = axes[2]
    im3 = ax3.imshow(encoded_data[0].T.numpy(), cmap='plasma', aspect='auto')
    ax3.set_title('After Adding Positional Encoding\n(Input + Position)', fontsize=12, fontweight='bold')
    ax3.set_xlabel('Position', fontsize=10)
    ax3.set_ylabel('Dimension Index', fontsize=10)
    ax3.set_xticks([0, 1, 2])
    ax3.set_xticklabels(['Week', 'Day', 'Hour'])
    plt.colorbar(im3, ax=ax3, shrink=0.8)
    
    plt.tight_layout()
    plt.savefig('result/positional_encoding_addition.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("位置编码相加图已保存到 result/positional_encoding_addition.png")

def save_analysis(pe_matrix, d_model, seq_len):
    """保存详细的数值分析"""
    print("保存详细分析...")
    
    with open('result/positional_encoding_detailed_analysis.txt', 'w', encoding='utf-8') as f:
        f.write("Transformer位置编码详细分析报告\n")
        f.write("=" * 80 + "\n\n")
        
        f.write("1. 位置编码公式:\n")
        f.write("-" * 40 + "\n")
        f.write("PE(pos, 2i)   = sin(pos / 10000^(2i/d_model))\n")
        f.write("PE(pos, 2i+1) = cos(pos / 10000^(2i/d_model))\n\n")
        
        f.write("2. 工程配置参数:\n")
        f.write("-" * 40 + "\n")
        f.write(f"d_model: {d_model} (模型维度)\n")
        f.write(f"seq_len: {seq_len} (序列长度)\n")
        f.write("位置含义: 0=Week, 1=Day, 2=Hour\n\n")
        
        f.write("3. 具体位置编码值:\n")
        f.write("-" * 40 + "\n")
        for pos in range(seq_len):
            position_name = ['Week', 'Day', 'Hour'][pos]
            f.write(f"\n位置 {pos} ({position_name}):\n")
            f.write("前16个维度的编码值:\n")
            for i in range(0, 16, 4):
                values = pe_matrix[pos, i:i+4]
                f.write(f"维度 {i:2d}-{i+3:2d}: {values}\n")
        
        f.write(f"\n4. 位置编码特点分析:\n")
        f.write("-" * 40 + "\n")
        f.write("- 位置0 (Week): 所有维度都是0或1，因为sin(0)=0, cos(0)=1\n")
        f.write("- 位置1 (Day): 根据不同频率产生不同的sin/cos值\n")
        f.write("- 位置2 (Hour): 频率更高，产生更复杂的编码模式\n")
        f.write("- 偶数维度使用sin函数，奇数维度使用cos函数\n")
        f.write("- 不同维度有不同的频率，形成独特的位置标识\n\n")
        
        f.write("5. 在地铁预测中的作用:\n")
        f.write("-" * 40 + "\n")
        f.write("- 帮助Transformer区分周期性、日期性、时刻性特征\n")
        f.write("- 为每个时间维度提供唯一的位置标识\n")
        f.write("- 通过加法方式与输入特征融合\n")
        f.write("- 保持模型对时间顺序的感知能力\n")
    
    print("详细分析已保存到 result/positional_encoding_detailed_analysis.txt")

def main():
    """主函数"""
    print("=" * 80)
    print("Transformer位置编码分析")
    print("=" * 80)
    
    analyze_and_visualize()
    
    print("\n" + "=" * 80)
    print("分析完成！生成的文件:")
    print("- result/positional_encoding_analysis.png (位置编码分析图)")
    print("- result/positional_encoding_addition.png (编码相加过程图)")
    print("- result/positional_encoding_detailed_analysis.txt (详细分析)")
    print("=" * 80)

if __name__ == "__main__":
    main()
