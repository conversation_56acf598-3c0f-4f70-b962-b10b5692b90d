#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可视化自适应权重融合机制
详细展示权重如何动态调整

Author: 008 - AI Algorithm Engineer for Subway Flow Prediction
"""

import numpy as np
import matplotlib.pyplot as plt
import os

def simulate_adaptive_weight_process():
    """模拟自适应权重融合过程"""
    print("正在模拟自适应权重融合过程...")
    
    # 创建模拟数据
    np.random.seed(42)
    n_points = 200
    
    # 生成真实值（带有趋势和周期性）
    x = np.linspace(0, 4*np.pi, n_points)
    true_values = 100 + 50 * np.sin(x) + 30 * np.sin(2*x) + np.random.normal(0, 5, n_points)
    
    # 生成main_predict预测（在某些区域表现较好）
    main_pred = true_values + np.random.normal(0, 8, n_points)
    # 在前半段添加系统性偏差
    main_pred[:n_points//2] += 10
    
    # 生成FFT预测（在另一些区域表现较好）
    fft_pred = true_values + np.random.normal(0, 6, n_points)
    # 在后半段添加系统性偏差
    fft_pred[n_points//2:] += 8
    
    return true_values, main_pred, fft_pred

def calculate_adaptive_weights(main_pred, fft_pred, true_values, window_size=20):
    """计算自适应权重"""
    print("正在计算自适应权重...")
    
    n_points = len(main_pred)
    main_weights = np.zeros(n_points)
    fft_weights = np.zeros(n_points)
    main_errors = np.zeros(n_points)
    fft_errors = np.zeros(n_points)
    adaptive_pred = np.zeros(n_points)
    
    for i in range(n_points):
        # 确定窗口范围
        start_idx = max(0, i - window_size)
        end_idx = min(n_points, i + window_size)
        
        if end_idx > start_idx:
            # 计算窗口内的平均绝对误差
            main_window_error = np.mean(np.abs(main_pred[start_idx:end_idx] - true_values[start_idx:end_idx]))
            fft_window_error = np.mean(np.abs(fft_pred[start_idx:end_idx] - true_values[start_idx:end_idx]))
            
            # 记录误差
            main_errors[i] = main_window_error
            fft_errors[i] = fft_window_error
            
            # 计算权重（交叉计算）
            total_error = main_window_error + fft_window_error + 1e-8
            main_weight = fft_window_error / total_error
            fft_weight = main_window_error / total_error
            
            # 记录权重
            main_weights[i] = main_weight
            fft_weights[i] = fft_weight
            
            # 计算融合预测
            adaptive_pred[i] = main_weight * main_pred[i] + fft_weight * fft_pred[i]
        else:
            # 边界情况
            main_weights[i] = 0.6
            fft_weights[i] = 0.4
            adaptive_pred[i] = 0.6 * main_pred[i] + 0.4 * fft_pred[i]
    
    return main_weights, fft_weights, main_errors, fft_errors, adaptive_pred

def plot_adaptive_weight_visualization(true_values, main_pred, fft_pred, 
                                     main_weights, fft_weights, main_errors, fft_errors, adaptive_pred):
    """绘制自适应权重可视化图"""
    print("正在绘制自适应权重可视化图...")
    
    # 设置字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    x_axis = range(len(true_values))
    
    # 子图1: 预测对比
    ax1 = axes[0, 0]
    ax1.plot(x_axis, true_values, 'g-', linewidth=2, label='True Values', alpha=0.8)
    ax1.plot(x_axis, main_pred, 'b--', linewidth=1.5, label='Main Predict', alpha=0.7)
    ax1.plot(x_axis, fft_pred, 'r--', linewidth=1.5, label='FFT Predict', alpha=0.7)
    ax1.plot(x_axis, adaptive_pred, 'm-', linewidth=2, label='Adaptive Fusion', alpha=0.9)
    ax1.set_title('Prediction Comparison', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Time Steps', fontsize=12)
    ax1.set_ylabel('Values', fontsize=12)
    ax1.legend(fontsize=10)
    ax1.grid(True, alpha=0.3)
    
    # 子图2: 权重变化
    ax2 = axes[0, 1]
    ax2.plot(x_axis, main_weights, 'b-', linewidth=2, label='Main Weight', alpha=0.8)
    ax2.plot(x_axis, fft_weights, 'r-', linewidth=2, label='FFT Weight', alpha=0.8)
    ax2.axhline(y=0.5, color='gray', linestyle=':', alpha=0.5, label='Equal Weight')
    ax2.set_title('Dynamic Weight Changes', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Time Steps', fontsize=12)
    ax2.set_ylabel('Weight', fontsize=12)
    ax2.set_ylim(0, 1)
    ax2.legend(fontsize=10)
    ax2.grid(True, alpha=0.3)
    
    # 子图3: 局部误差
    ax3 = axes[1, 0]
    ax3.plot(x_axis, main_errors, 'b-', linewidth=2, label='Main Local Error', alpha=0.8)
    ax3.plot(x_axis, fft_errors, 'r-', linewidth=2, label='FFT Local Error', alpha=0.8)
    ax3.set_title('Local Window Errors', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Time Steps', fontsize=12)
    ax3.set_ylabel('Mean Absolute Error', fontsize=12)
    ax3.legend(fontsize=10)
    ax3.grid(True, alpha=0.3)
    
    # 子图4: 权重分布统计
    ax4 = axes[1, 1]
    
    # 创建权重分布的直方图
    bins = np.linspace(0, 1, 21)
    ax4.hist(main_weights, bins=bins, alpha=0.6, label='Main Weight Distribution', color='blue', density=True)
    ax4.hist(fft_weights, bins=bins, alpha=0.6, label='FFT Weight Distribution', color='red', density=True)
    ax4.axvline(x=np.mean(main_weights), color='blue', linestyle='--', linewidth=2, 
               label=f'Main Avg: {np.mean(main_weights):.3f}')
    ax4.axvline(x=np.mean(fft_weights), color='red', linestyle='--', linewidth=2, 
               label=f'FFT Avg: {np.mean(fft_weights):.3f}')
    ax4.set_title('Weight Distribution', fontsize=14, fontweight='bold')
    ax4.set_xlabel('Weight Value', fontsize=12)
    ax4.set_ylabel('Density', fontsize=12)
    ax4.legend(fontsize=10)
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if not os.path.exists('result'):
        os.makedirs('result')
    
    plt.savefig('result/adaptive_weight_mechanism_visualization.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("可视化图已保存到 result/adaptive_weight_mechanism_visualization.png")

def analyze_weight_behavior(main_weights, fft_weights, main_errors, fft_errors):
    """分析权重行为"""
    print("正在分析权重行为...")
    
    # 计算统计信息
    main_weight_avg = np.mean(main_weights)
    fft_weight_avg = np.mean(fft_weights)
    main_weight_std = np.std(main_weights)
    fft_weight_std = np.std(fft_weights)
    
    # 找到权重变化最大的区域
    weight_diff = np.abs(main_weights - fft_weights)
    max_diff_idx = np.argmax(weight_diff)
    
    # 计算权重切换次数
    main_dominant = main_weights > fft_weights
    switches = np.sum(np.diff(main_dominant.astype(int)) != 0)
    
    print(f"\n权重行为分析:")
    print(f"Main权重 - 平均: {main_weight_avg:.3f}, 标准差: {main_weight_std:.3f}")
    print(f"FFT权重  - 平均: {fft_weight_avg:.3f}, 标准差: {fft_weight_std:.3f}")
    print(f"最大权重差异: {weight_diff[max_diff_idx]:.3f} (位置: {max_diff_idx})")
    print(f"主导权重切换次数: {switches}")
    
    # 分析不同阶段的权重分布
    n_points = len(main_weights)
    stage1 = slice(0, n_points//3)
    stage2 = slice(n_points//3, 2*n_points//3)
    stage3 = slice(2*n_points//3, n_points)
    
    print(f"\n阶段性分析:")
    print(f"前1/3阶段 - Main权重: {np.mean(main_weights[stage1]):.3f}, FFT权重: {np.mean(fft_weights[stage1]):.3f}")
    print(f"中1/3阶段 - Main权重: {np.mean(main_weights[stage2]):.3f}, FFT权重: {np.mean(fft_weights[stage2]):.3f}")
    print(f"后1/3阶段 - Main权重: {np.mean(main_weights[stage3]):.3f}, FFT权重: {np.mean(fft_weights[stage3]):.3f}")

def create_detailed_explanation():
    """创建详细的机制解释"""
    print("正在创建详细的机制解释...")
    
    explanation = """
# 自适应权重融合机制可视化分析

## 🎯 核心机制

自适应权重融合通过以下步骤实现智能混合：

1. **滑动窗口评估**: 使用固定大小的窗口评估局部性能
2. **交叉权重计算**: 误差小的模型获得大权重
3. **动态权重调整**: 权重随时间和性能变化自动调整
4. **加权融合**: 按计算权重组合预测结果

## 📊 可视化图表说明

### 图1: 预测对比 (Prediction Comparison)
- **绿色实线**: 真实值
- **蓝色虚线**: Main Predict预测
- **红色虚线**: FFT预测  
- **紫色实线**: 自适应融合结果

### 图2: 权重变化 (Dynamic Weight Changes)
- **蓝色线**: Main模型权重
- **红色线**: FFT模型权重
- **灰色虚线**: 等权重线(0.5)

### 图3: 局部误差 (Local Window Errors)
- **蓝色线**: Main模型在滑动窗口内的平均绝对误差
- **红色线**: FFT模型在滑动窗口内的平均绝对误差

### 图4: 权重分布 (Weight Distribution)
- **蓝色直方图**: Main权重的分布
- **红色直方图**: FFT权重的分布
- **虚线**: 平均权重值

## 🔍 关键观察

1. **权重反比关系**: 当一个模型误差增大时，其权重减小
2. **动态平衡**: 权重会根据局部性能动态调整
3. **平滑过渡**: 权重变化是连续的，避免突然跳跃
4. **自适应性**: 系统自动识别并利用每个模型的优势区域

这种机制确保了融合模型能够在不同时段选择最优的预测策略。
"""
    
    with open('result/adaptive_weight_mechanism_explanation.txt', 'w', encoding='utf-8') as f:
        f.write(explanation)
    
    print("详细解释已保存到 result/adaptive_weight_mechanism_explanation.txt")

def main():
    """主函数"""
    print("=" * 80)
    print("自适应权重融合机制可视化")
    print("=" * 80)
    
    # 1. 模拟数据
    true_values, main_pred, fft_pred = simulate_adaptive_weight_process()
    
    # 2. 计算自适应权重
    main_weights, fft_weights, main_errors, fft_errors, adaptive_pred = calculate_adaptive_weights(
        main_pred, fft_pred, true_values, window_size=20)
    
    # 3. 可视化
    plot_adaptive_weight_visualization(true_values, main_pred, fft_pred, 
                                     main_weights, fft_weights, main_errors, fft_errors, adaptive_pred)
    
    # 4. 分析权重行为
    analyze_weight_behavior(main_weights, fft_weights, main_errors, fft_errors)
    
    # 5. 创建详细解释
    create_detailed_explanation()
    
    # 6. 计算性能指标
    from sklearn.metrics import mean_squared_error, mean_absolute_error
    
    main_rmse = np.sqrt(mean_squared_error(true_values, main_pred))
    fft_rmse = np.sqrt(mean_squared_error(true_values, fft_pred))
    adaptive_rmse = np.sqrt(mean_squared_error(true_values, adaptive_pred))
    
    main_mae = mean_absolute_error(true_values, main_pred)
    fft_mae = mean_absolute_error(true_values, fft_pred)
    adaptive_mae = mean_absolute_error(true_values, adaptive_pred)
    
    print(f"\n性能对比:")
    print(f"Main Predict - RMSE: {main_rmse:.3f}, MAE: {main_mae:.3f}")
    print(f"FFT Method   - RMSE: {fft_rmse:.3f}, MAE: {fft_mae:.3f}")
    print(f"Adaptive     - RMSE: {adaptive_rmse:.3f}, MAE: {adaptive_mae:.3f}")
    
    print(f"\n改进效果:")
    main_improvement = ((main_rmse - adaptive_rmse) / main_rmse * 100)
    fft_improvement = ((fft_rmse - adaptive_rmse) / fft_rmse * 100)
    print(f"相比Main Predict改进: {main_improvement:+.1f}%")
    print(f"相比FFT Method改进: {fft_improvement:+.1f}%")
    
    print("\n可视化完成！")
    print("生成文件:")
    print("- result/adaptive_weight_mechanism_visualization.png")
    print("- result/adaptive_weight_mechanism_explanation.txt")

if __name__ == "__main__":
    main()
