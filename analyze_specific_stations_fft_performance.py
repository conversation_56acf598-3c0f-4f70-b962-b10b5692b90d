#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于fft_continuous_prediction.py的方法
统计车站30,14,95,97,262,33的数据集后五天高峰10个时间步的总RMSE、MAE、WMAPE值

Author: 008 - AI Algorithm Engineer for Subway Flow Prediction
"""

import numpy as np
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import os

class FFTPredictor(nn.Module):
    """基于FFT的多层感知机预测器"""
    
    def __init__(self, input_size, hidden_size=64, output_size=10):
        super(FFTPredictor, self).__init__()
        self.mlp = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_size // 2, output_size)
        )
    
    def forward(self, x):
        return self.mlp(x)

class SpecificStationsFFTAnalyzer:
    """指定车站FFT预测分析器"""
    
    def __init__(self, target_stations, sequence_length=10):
        self.target_stations = target_stations
        self.sequence_length = sequence_length
        self.scaler = MinMaxScaler()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        print(f"分析车站: {target_stations}")
    
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        
        # 加载原始数据
        raw_data = np.loadtxt('./data/in_15min.csv', delimiter=",")
        if raw_data.shape[0] != 276:
            raw_data = raw_data.T
        
        print(f"原始数据形状: {raw_data.shape}")
        
        # 加载高峰时刻信息
        peak_times = np.loadtxt('./data/continuous_peak_flow_dataset_peak_times.csv', delimiter=',', dtype=int)
        
        # 数据集参数
        self.station_num = 276
        self.time_steps_per_day = 72
        self.total_days = 25
        self.train_val_days = 20
        self.test_days = 5
        
        return raw_data, peak_times
    
    def create_multi_day_dataset(self, raw_data, peak_times):
        """创建多天数据集"""
        print("正在构建多天数据集...")
        
        multi_day_data = []
        
        for station_idx in range(self.station_num):
            station_data = []
            peak_time = peak_times[station_idx]
            
            # 收集每一天相同时刻的10个时间步序列
            for day in range(self.total_days):
                day_start = day * self.time_steps_per_day
                
                # 计算当天对应的序列位置
                start_idx = max(0, day_start + peak_time - 4)
                end_idx = min(raw_data.shape[1], day_start + peak_time + 6)
                
                # 获取序列
                if end_idx - start_idx >= self.sequence_length:
                    sequence = raw_data[station_idx, start_idx:start_idx + self.sequence_length]
                else:
                    # 处理边界情况
                    sequence = raw_data[station_idx, start_idx:end_idx]
                    if len(sequence) < self.sequence_length:
                        sequence = np.pad(sequence, (0, self.sequence_length - len(sequence)), 'edge')
                
                station_data.append(sequence)
            
            multi_day_data.append(station_data)
        
        multi_day_data = np.array(multi_day_data)  # (stations, days, sequence_length)
        print(f"多天数据集形状: {multi_day_data.shape}")
        
        return multi_day_data
    
    def split_dataset(self, multi_day_data):
        """划分训练集、验证集、测试集"""
        print("正在划分数据集...")

        # 确保测试集是最后5天
        train_val_days = self.total_days - self.test_days  # 20天
        train_size = int(train_val_days * 0.8)  # 16天训练
        val_size = train_val_days - train_size  # 4天验证

        # 划分数据
        train_data = multi_day_data[:, :train_size, :]
        val_data = multi_day_data[:, train_size:train_size + val_size, :]
        test_data = multi_day_data[:, train_size + val_size:, :]  # 最后5天

        print(f"训练集形状: {train_data.shape} (前{train_size}天)")
        print(f"验证集形状: {val_data.shape} (第{train_size+1}-{train_size+val_size}天)")
        print(f"测试集形状: {test_data.shape} (最后{self.test_days}天)")

        return train_data, val_data, test_data
    
    def extract_fft_features(self, sequences):
        """提取FFT特征"""
        fft_features = []
        
        for sequence in sequences:
            # 应用FFT
            fft_result = np.fft.fft(sequence)
            
            # 提取幅度和相位
            magnitude = np.abs(fft_result)
            phase = np.angle(fft_result)
            
            # 只保留前一半频率（由于对称性）
            half_len = len(magnitude) // 2
            magnitude = magnitude[:half_len]
            phase = phase[:half_len]
            
            # 合并特征
            features = np.concatenate([magnitude, phase])
            fft_features.append(features)
        
        return np.array(fft_features)
    
    def prepare_training_data(self, train_data, val_data):
        """准备训练数据"""
        print("正在准备训练数据...")
        
        # 重塑数据
        train_sequences = train_data.reshape(-1, self.sequence_length)
        val_sequences = val_data.reshape(-1, self.sequence_length)
        
        # 数据归一化
        train_sequences_norm = self.scaler.fit_transform(train_sequences)
        val_sequences_norm = self.scaler.transform(val_sequences)
        
        # 提取FFT特征
        train_fft_features = self.extract_fft_features(train_sequences_norm)
        val_fft_features = self.extract_fft_features(val_sequences_norm)
        
        # 输入：FFT特征，输出：完整的10个时刻
        X_train = train_fft_features
        y_train = train_sequences_norm
        
        X_val = val_fft_features
        y_val = val_sequences_norm
        
        print(f"训练数据准备完成: X_train shape: {X_train.shape}, y_train shape: {y_train.shape}")
        
        return X_train, y_train, X_val, y_val
    
    def train_model(self, X_train, y_train, X_val, y_val, epochs=100, batch_size=32, lr=0.001):
        """训练FFT预测模型"""
        print("正在训练FFT预测模型...")
        
        # 转换为PyTorch张量
        X_train_tensor = torch.FloatTensor(X_train).to(self.device)
        y_train_tensor = torch.FloatTensor(y_train).to(self.device)
        X_val_tensor = torch.FloatTensor(X_val).to(self.device)
        y_val_tensor = torch.FloatTensor(y_val).to(self.device)
        
        # 创建模型
        input_size = X_train.shape[1]
        output_size = y_train.shape[1]
        self.model = FFTPredictor(input_size, output_size=output_size).to(self.device)
        
        # 优化器和损失函数
        optimizer = torch.optim.Adam(self.model.parameters(), lr=lr)
        criterion = nn.MSELoss()
        
        # 训练循环
        for epoch in range(epochs):
            # 训练阶段
            self.model.train()
            train_loss = 0
            
            for i in range(0, len(X_train_tensor), batch_size):
                batch_X = X_train_tensor[i:i+batch_size]
                batch_y = y_train_tensor[i:i+batch_size]
                
                optimizer.zero_grad()
                outputs = self.model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
            
            # 验证阶段
            self.model.eval()
            with torch.no_grad():
                val_outputs = self.model(X_val_tensor)
                val_loss = criterion(val_outputs, y_val_tensor).item()
            
            if epoch % 20 == 0:
                print(f'Epoch {epoch}, Train Loss: {train_loss/(len(X_train_tensor)//batch_size):.6f}, Val Loss: {val_loss:.6f}')
    
    def predict_specific_stations(self, test_data, peak_times):
        """预测指定车站的性能"""
        print(f"正在预测指定车站{self.target_stations}的性能...")
        
        all_predictions = []
        all_true_values = []
        station_data = {}
        
        for station_idx in self.target_stations:
            station_predictions = []
            station_true_values = []
            
            # 对每个测试周期进行预测
            for day in range(test_data.shape[1]):
                sequence = test_data[station_idx, day, :]  # 10个时间步
                
                # 归一化
                sequence_norm = self.scaler.transform(sequence.reshape(1, -1))[0]
                
                # 提取FFT特征
                fft_features = self.extract_fft_features([sequence_norm])[0]
                
                # 预测完整的10个时间步
                self.model.eval()
                with torch.no_grad():
                    X_tensor = torch.FloatTensor(fft_features).unsqueeze(0).to(self.device)
                    pred_norm = self.model(X_tensor).cpu().numpy()[0]
                
                # 反归一化预测结果
                pred_full = self.scaler.inverse_transform(pred_norm.reshape(1, -1))
                predicted_sequence = pred_full[0]
                
                station_predictions.append(predicted_sequence)
                station_true_values.append(sequence)
                all_predictions.extend(predicted_sequence)
                all_true_values.extend(sequence)
            
            station_data[station_idx] = {
                'predictions': np.array(station_predictions),
                'true_values': np.array(station_true_values),
                'peak_time': peak_times[station_idx]
            }
        
        all_predictions = np.array(all_predictions)
        all_true_values = np.array(all_true_values)
        
        return all_predictions, all_true_values, station_data
    
    def calculate_metrics(self, y_true, y_pred):
        """计算评估指标"""
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        mae = mean_absolute_error(y_true, y_pred)
        
        # 计算WMAPE
        mask = y_true > 0
        if np.sum(mask) > 0:
            wmape = np.sum(np.abs(y_pred[mask] - y_true[mask])) / np.sum(y_true[mask])
        else:
            wmape = 0
        
        r2 = r2_score(y_true, y_pred)
        
        return rmse, mae, wmape, r2
    
    def calculate_station_metrics(self, station_data):
        """计算各车站的评估指标"""
        print("正在计算各车站的评估指标...")
        
        station_metrics = {}
        
        for station_idx in self.target_stations:
            data = station_data[station_idx]
            predictions = data['predictions'].flatten()
            true_values = data['true_values'].flatten()
            
            rmse, mae, wmape, r2 = self.calculate_metrics(true_values, predictions)
            
            station_metrics[station_idx] = {
                'rmse': rmse,
                'mae': mae,
                'wmape': wmape,
                'r2': r2,
                'peak_time': data['peak_time'],
                'count': len(predictions),
                'pred_mean': np.mean(predictions),
                'true_mean': np.mean(true_values)
            }
            
            print(f"车站{station_idx}: RMSE={rmse:.4f}, MAE={mae:.4f}, WMAPE={wmape:.4f}")
        
        return station_metrics
    
    def save_results(self, overall_rmse, overall_mae, overall_wmape, overall_r2,
                    station_metrics, all_predictions, all_true_values):
        """保存分析结果"""
        if not os.path.exists('result'):
            os.makedirs('result')
        
        with open('result/specific_stations_fft_performance.txt', 'w', encoding='utf-8') as f:
            f.write("指定车站FFT高峰期10个时间步预测性能分析\n")
            f.write("=" * 80 + "\n\n")
            
            f.write("分析说明:\n")
            f.write("-" * 40 + "\n")
            f.write(f"- 分析车站: {self.target_stations}\n")
            f.write("- 基于fft_continuous_prediction.py方法\n")
            f.write("- 数据来源: FFT模型的预测结果\n")
            f.write("- 评估范围: 测试集(最后5天)的高峰时刻左右10个时间步\n")
            f.write("- 每个车站: 10个时间步 × 5天 = 50个预测值\n")
            f.write(f"- 总计: {len(self.target_stations)}个车站 × 50 = {len(all_predictions)}个预测值\n\n")
            
            f.write("整体评估指标(指定车站FFT高峰时刻序列):\n")
            f.write("-" * 40 + "\n")
            f.write(f"总RMSE: {overall_rmse:.4f}\n")
            f.write(f"总MAE: {overall_mae:.4f}\n")
            f.write(f"总WMAPE: {overall_wmape:.4f} ({overall_wmape*100:.2f}%)\n")
            f.write(f"总R2: {overall_r2:.4f}\n\n")
            
            f.write("各车站详细指标:\n")
            f.write("-" * 80 + "\n")
            f.write("车站ID | 高峰时刻 | RMSE     | MAE      | WMAPE    | R2       | 数据点数 | 预测均值 | 真实均值\n")
            f.write("-" * 80 + "\n")
            
            for station_idx in self.target_stations:
                if station_idx in station_metrics:
                    metrics = station_metrics[station_idx]
                    peak_time = metrics['peak_time']
                    hour = peak_time // 4
                    minute = (peak_time % 4) * 15
                    
                    f.write(f"{station_idx:6d} | {hour:02d}:{minute:02d}     | {metrics['rmse']:8.4f} | {metrics['mae']:8.4f} | "
                           f"{metrics['wmape']:8.4f} | {metrics['r2']:8.4f} | {metrics['count']:8d} | "
                           f"{metrics['pred_mean']:8.2f} | {metrics['true_mean']:8.2f}\n")
            
            f.write("\n数据统计:\n")
            f.write("-" * 40 + "\n")
            f.write(f"总预测值范围: [{np.min(all_predictions):.2f}, {np.max(all_predictions):.2f}]\n")
            f.write(f"总真实值范围: [{np.min(all_true_values):.2f}, {np.max(all_true_values):.2f}]\n")
            f.write(f"总预测值平均: {np.mean(all_predictions):.2f}\n")
            f.write(f"总真实值平均: {np.mean(all_true_values):.2f}\n")
        
        print("分析结果已保存到 result/specific_stations_fft_performance.txt")

def main():
    """主函数"""
    print("=" * 80)
    print("指定车站FFT高峰期10个时间步预测性能分析")
    print("基于fft_continuous_prediction.py方法")
    print("=" * 80)
    
    # 指定的车站
    target_stations = [30, 14, 95, 97, 262, 33]
    
    # 创建分析器
    analyzer = SpecificStationsFFTAnalyzer(target_stations)
    
    # 1. 加载数据
    raw_data, peak_times = analyzer.load_data()
    
    # 2. 创建多天数据集
    multi_day_data = analyzer.create_multi_day_dataset(raw_data, peak_times)
    
    # 3. 划分数据集
    train_data, val_data, test_data = analyzer.split_dataset(multi_day_data)
    
    # 4. 准备训练数据
    X_train, y_train, X_val, y_val = analyzer.prepare_training_data(train_data, val_data)
    
    # 5. 训练模型
    analyzer.train_model(X_train, y_train, X_val, y_val, epochs=200)
    
    # 6. 预测指定车站
    all_predictions, all_true_values, station_data = analyzer.predict_specific_stations(test_data, peak_times)
    
    # 7. 计算整体评估指标
    overall_rmse, overall_mae, overall_wmape, overall_r2 = analyzer.calculate_metrics(
        all_true_values, all_predictions)
    
    # 8. 计算各车站级别的指标
    station_metrics = analyzer.calculate_station_metrics(station_data)
    
    # 9. 输出结果
    print("\n" + "=" * 80)
    print(f"车站{target_stations}FFT高峰期10个时间步预测性能:")
    print("-" * 80)
    print(f"总数据点数: {len(all_predictions)} ({len(target_stations)}个车站 × 5天 × 10时间步)")
    print(f"总RMSE: {overall_rmse:.4f}")
    print(f"总MAE: {overall_mae:.4f}")
    print(f"总WMAPE: {overall_wmape:.4f} ({overall_wmape*100:.2f}%)")
    print(f"总R2: {overall_r2:.4f}")
    print("=" * 80)
    
    # 10. 保存详细结果
    analyzer.save_results(overall_rmse, overall_mae, overall_wmape, overall_r2,
                         station_metrics, all_predictions, all_true_values)
    
    print("\n分析完成！")
    print("这些指标反映了指定车站基于FFT方法的高峰时刻序列预测准确性。")

if __name__ == "__main__":
    main()
