#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
绘制车站33,244,69,78的预测值和真实值对比曲线
使用红色和蓝色，保持简洁的标题样式

Author: 008 - AI Algorithm Engineer for Subway Flow Prediction
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import os
from sklearn.metrics import mean_squared_error, mean_absolute_error

# 设置matplotlib后端和中文字体
matplotlib.use('Agg')
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_main_model_results():
    """加载主模型(GCN+Transformer)的完整测试集结果"""
    print("正在加载主模型的完整测试集结果...")
    
    try:
        # 加载主模型的完整预测结果
        main_predictions = np.loadtxt('result/improved_ALL_276_stations_predictions.txt')
        main_true_values = np.loadtxt('result/improved_ALL_276_stations_original.txt')
        
        print(f"主模型数据形状: 预测{main_predictions.shape}, 真实{main_true_values.shape}")
        return main_predictions, main_true_values
        
    except Exception as e:
        print(f"加载主模型结果失败: {e}")
        return None, None

def extract_station_test_data(station_ids, main_predictions, main_true_values):
    """提取指定车站的完整测试集数据"""
    print("正在提取指定车站的完整测试集数据...")
    
    station_data = {}
    
    for station_id in station_ids:
        print(f"处理车站 {station_id}...")
        
        # 提取该车站的完整测试集预测和真实值
        station_pred = main_predictions[station_id, :]
        station_true = main_true_values[station_id, :]
        
        station_data[station_id] = {
            'predictions': station_pred,
            'true_values': station_true,
            'length': len(station_pred)
        }
        
        print(f"车站{station_id}: 测试集长度 {len(station_pred)} 个时间步")
    
    return station_data

def calculate_metrics(y_true, y_pred):
    """计算RMSE、MAE、WMAPE指标"""
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    mae = mean_absolute_error(y_true, y_pred)
    wmape = np.sum(np.abs(y_true - y_pred)) / (np.sum(np.abs(y_true)) + 1e-8)
    return rmse, mae, wmape

def plot_simple_comparison_curves(station_data, station_ids):
    """绘制简洁的对比曲线"""
    print("正在绘制简洁的对比曲线...")

    # 创建2x2的子图布局
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.flatten()

    # 定义车站标签映射
    station_labels = ['A', 'B', 'C', 'D']

    for i, station_id in enumerate(station_ids):
        ax = axes[i]
        data = station_data[station_id]

        # 获取数据
        true_values = data['true_values']
        predictions = data['predictions']
        test_length = data['length']

        # 横坐标
        x_axis = range(test_length)

        # 计算指标
        rmse, mae, wmape = calculate_metrics(true_values, predictions)

        # 绘制曲线 - 使用红色和蓝色
        ax.plot(x_axis, true_values, 'r-', linewidth=2, alpha=0.8, label='True Values')
        ax.plot(x_axis, predictions, 'b-', linewidth=1.5, alpha=0.7, label='Predictions')

        # 设置简洁的标题 - 使用A B C D标签
        ax.set_title(f'Station {station_labels[i]} \n',
                    fontsize=14, fontweight='bold')

        ax.set_xlabel('Time Steps', fontsize=12)
        ax.set_ylabel('Passenger Flow', fontsize=12)
        ax.legend(fontsize=10, loc='upper right')
        ax.grid(True, alpha=0.3)

        # 设置坐标轴范围，确保显示完整
        ax.set_xlim(0, test_length-1)
        y_min = min(np.min(true_values), np.min(predictions))
        y_max = max(np.max(true_values), np.max(predictions))
        y_range = y_max - y_min
        ax.set_ylim(y_min - y_range*0.05, y_max + y_range*0.05)
    
    plt.tight_layout()
    
    # 确保result目录存在
    if not os.path.exists('result'):
        os.makedirs('result')
    
    plt.savefig('result/stations_33_244_69_78_simple_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("简洁对比曲线已保存到 result/stations_33_244_69_78_simple_comparison.png")

def create_simple_summary(station_data, station_ids):
    """创建简单的性能汇总"""
    print("正在创建简单的性能汇总...")
    
    with open('result/stations_33_244_69_78_simple_summary.txt', 'w', encoding='utf-8') as f:
        f.write("车站33,244,69,78预测性能汇总\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("各车站性能:\n")
        f.write("-" * 60 + "\n")
        f.write(f"{'车站ID':<8} {'RMSE':<10} {'MAE':<10} {'WMAPE':<12}\n")
        f.write("-" * 60 + "\n")
        
        for station_id in station_ids:
            if station_id in station_data:
                data = station_data[station_id]
                true_values = data['true_values']
                predictions = data['predictions']
                
                rmse, mae, wmape = calculate_metrics(true_values, predictions)
                
                f.write(f"{station_id:<8} {rmse:<10.4f} {mae:<10.4f} {wmape:<12.4f}\n")
    
    print("性能汇总已保存到 result/stations_33_244_69_78_simple_summary.txt")

def main():
    """主函数"""
    print("=" * 60)
    print("绘制车站33,244,69,78简洁对比曲线")
    print("使用红色(真实值)和蓝色(预测值)")
    print("=" * 60)
    
    # 指定的4个车站
    station_ids = [33, 244, 69, 78]
    
    # 1. 加载主模型结果
    main_predictions, main_true_values = load_main_model_results()
    
    if main_predictions is None:
        print("无法加载主模型结果，退出程序")
        return
    
    # 2. 提取指定车站的完整测试集数据
    station_data = extract_station_test_data(station_ids, main_predictions, main_true_values)
    
    # 3. 绘制简洁对比曲线
    plot_simple_comparison_curves(station_data, station_ids)
    
    # 4. 创建简单汇总
    create_simple_summary(station_data, station_ids)
    
    # 5. 输出简要结果
    print("\n各车站性能:")
    print("-" * 50)
    print(f"{'车站ID':<8} {'RMSE':<10} {'MAE':<10} {'WMAPE':<10}")
    print("-" * 50)
    
    for station_id in station_ids:
        if station_id in station_data:
            data = station_data[station_id]
            true_values = data['true_values']
            predictions = data['predictions']
            
            rmse, mae, wmape = calculate_metrics(true_values, predictions)
            
            print(f"{station_id:<8} {rmse:<10.4f} {mae:<10.4f} {wmape:<10.4f}")
    
    print("\n绘制完成！")
    print("生成文件:")
    print("- result/stations_33_244_69_78_simple_comparison.png (简洁对比曲线)")
    print("- result/stations_33_244_69_78_simple_summary.txt (性能汇总)")
    print("=" * 60)

if __name__ == "__main__":
    main()
