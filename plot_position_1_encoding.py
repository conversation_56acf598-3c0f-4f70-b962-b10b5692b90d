#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
绘制位置1的编码值图表
横坐标：维度 (0-63)
纵坐标：编码值

Author: 008 - AI Algorithm Engineer for Subway Flow Prediction
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import math
import os

# 设置matplotlib后端
import matplotlib
matplotlib.use('Agg')

class PositionalEncoding(nn.Module):
    """位置编码模块"""
    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        self.register_buffer('pe', pe)

    def forward(self, x):
        return x + self.pe[:, :x.size(1), :]

def analyze_position_encoding_parameters():
    """分析位置编码的参数配置"""
    print("分析位置编码参数配置...")
    
    # 工程中的配置
    d_model = 64      # 模型维度
    max_len = 5000    # 最大序列长度
    seq_len = 3       # 实际使用的序列长度
    
    print(f"d_model: {d_model}")
    print(f"max_len: {max_len} (支持的最大位置数)")
    print(f"seq_len: {seq_len} (工程中实际使用的位置数)")
    print(f"实际使用的位置: 0 (Week), 1 (Day), 2 (Hour)")
    
    return d_model, max_len, seq_len

def get_position_1_encoding(d_model=64):
    """获取位置1的完整编码值"""
    print("获取位置1的编码值...")
    
    # 创建位置编码
    pos_encoder = PositionalEncoding(d_model)
    
    # 获取位置1的编码
    position_1_encoding = pos_encoder.pe.squeeze(0)[1, :].numpy()
    
    print(f"位置1编码形状: {position_1_encoding.shape}")
    print(f"位置1编码值范围: [{position_1_encoding.min():.6f}, {position_1_encoding.max():.6f}]")
    
    return position_1_encoding

def plot_position_1_encoding_detailed(position_1_encoding, d_model=64):
    """绘制位置1编码值的详细图表"""
    print("绘制位置1编码值详细图表...")
    
    # 确保result目录存在
    if not os.path.exists('result'):
        os.makedirs('result')
    
    # 创建维度索引
    dimensions = np.arange(d_model)
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 子图1: 完整的64个维度
    ax1 = axes[0, 0]
    bars = ax1.bar(dimensions, position_1_encoding, alpha=0.7, 
                   color=['red' if i % 2 == 0 else 'blue' for i in range(d_model)])
    ax1.set_title('Position 1 Encoding Values (All 64 Dimensions)\nRed: sin (even), Blue: cos (odd)', 
                  fontsize=12, fontweight='bold')
    ax1.set_xlabel('Dimension Index', fontsize=10)
    ax1.set_ylabel('Encoding Value', fontsize=10)
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(-1, d_model)
    
    # 添加零线
    ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    # 子图2: 前16个维度的详细视图
    ax2 = axes[0, 1]
    first_16_dims = dimensions[:16]
    first_16_values = position_1_encoding[:16]
    bars2 = ax2.bar(first_16_dims, first_16_values, alpha=0.8,
                    color=['red' if i % 2 == 0 else 'blue' for i in range(16)])
    ax2.set_title('Position 1 Encoding Values (First 16 Dimensions)\nDetailed View', 
                  fontsize=12, fontweight='bold')
    ax2.set_xlabel('Dimension Index', fontsize=10)
    ax2.set_ylabel('Encoding Value', fontsize=10)
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, v in enumerate(first_16_values):
        ax2.text(i, v + 0.02 if v >= 0 else v - 0.05, f'{v:.3f}', 
                ha='center', va='bottom' if v >= 0 else 'top', fontsize=8)
    
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    # 子图3: sin和cos分别显示
    ax3 = axes[1, 0]
    sin_dims = dimensions[::2]  # 偶数维度
    cos_dims = dimensions[1::2]  # 奇数维度
    sin_values = position_1_encoding[::2]
    cos_values = position_1_encoding[1::2]
    
    ax3.plot(sin_dims, sin_values, 'ro-', label='sin (even dimensions)', 
             linewidth=2, markersize=4, alpha=0.8)
    ax3.plot(cos_dims, cos_values, 'bs-', label='cos (odd dimensions)', 
             linewidth=2, markersize=4, alpha=0.8)
    ax3.set_title('Position 1: Sin vs Cos Values', fontsize=12, fontweight='bold')
    ax3.set_xlabel('Dimension Index', fontsize=10)
    ax3.set_ylabel('Encoding Value', fontsize=10)
    ax3.legend(fontsize=10)
    ax3.grid(True, alpha=0.3)
    ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    # 子图4: 频率分析
    ax4 = axes[1, 1]
    # 计算每个维度对应的频率
    frequencies = []
    for i in range(d_model // 2):
        freq = 1 / (10000 ** (2 * i / d_model))
        frequencies.append(freq)
        frequencies.append(freq)  # sin和cos使用相同频率
    
    ax4.semilogy(dimensions, frequencies, 'g.-', linewidth=2, markersize=4)
    ax4.set_title('Frequency for Each Dimension', fontsize=12, fontweight='bold')
    ax4.set_xlabel('Dimension Index', fontsize=10)
    ax4.set_ylabel('Frequency (log scale)', fontsize=10)
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('result/position_1_encoding_detailed.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("位置1编码详细图已保存到 result/position_1_encoding_detailed.png")

def plot_position_1_simple(position_1_encoding, d_model=64):
    """绘制位置1编码的简单图表（按要求：横坐标维度，纵坐标编码值）"""
    print("绘制位置1编码简单图表...")
    
    # 创建维度索引
    dimensions = np.arange(d_model)
    
    # 创建图表
    fig, ax = plt.subplots(1, 1, figsize=(14, 8))
    
    # 绘制柱状图
    bars = ax.bar(dimensions, position_1_encoding, alpha=0.7, 
                  color=['red' if i % 2 == 0 else 'blue' for i in range(d_model)],
                  edgecolor='black', linewidth=0.5)
    
    # 设置标题和标签
    ax.set_title('Position 1 (Day) Encoding Values\nRed: sin(even dimensions), Blue: cos(odd dimensions)', 
                 fontsize=14, fontweight='bold')
    ax.set_xlabel('Dimension Index (0-63)', fontsize=12)
    ax.set_ylabel('Encoding Value', fontsize=12)
    
    # 添加网格和零线
    ax.grid(True, alpha=0.3)
    ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    
    # 设置x轴刻度
    ax.set_xticks(range(0, d_model, 4))
    ax.set_xlim(-0.5, d_model - 0.5)
    
    # 添加图例
    from matplotlib.patches import Patch
    legend_elements = [Patch(facecolor='red', alpha=0.7, label='sin (even dimensions)'),
                      Patch(facecolor='blue', alpha=0.7, label='cos (odd dimensions)')]
    ax.legend(handles=legend_elements, fontsize=11)
    
    # 添加一些关键点的数值标签
    key_points = [0, 1, 2, 3, 8, 9, 16, 17, 32, 33, 48, 49, 62, 63]
    for point in key_points:
        if point < len(position_1_encoding):
            value = position_1_encoding[point]
            ax.annotate(f'{value:.3f}', 
                       xy=(point, value), 
                       xytext=(point, value + 0.1 if value >= 0 else value - 0.1),
                       ha='center', va='bottom' if value >= 0 else 'top',
                       fontsize=8, alpha=0.8)
    
    plt.tight_layout()
    plt.savefig('result/position_1_encoding_simple.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("位置1编码简单图已保存到 result/position_1_encoding_simple.png")

def create_numerical_analysis(position_1_encoding, d_model=64):
    """创建位置1编码的数值分析"""
    print("创建位置1编码数值分析...")
    
    with open('result/position_1_encoding_analysis.txt', 'w', encoding='utf-8') as f:
        f.write("位置1 (Day) 编码值详细分析\n")
        f.write("=" * 80 + "\n\n")
        
        f.write("1. 基本参数:\n")
        f.write("-" * 40 + "\n")
        f.write(f"d_model: {d_model}\n")
        f.write(f"位置索引: 1 (Day)\n")
        f.write(f"编码维度: 0 到 {d_model-1}\n")
        f.write(f"编码值范围: [{position_1_encoding.min():.6f}, {position_1_encoding.max():.6f}]\n\n")
        
        f.write("2. 完整编码值 (所有64个维度):\n")
        f.write("-" * 40 + "\n")
        for i in range(0, d_model, 8):
            end_idx = min(i + 8, d_model)
            values = position_1_encoding[i:end_idx]
            f.write(f"维度 {i:2d}-{end_idx-1:2d}: ")
            f.write(" ".join([f"{v:8.6f}" for v in values]))
            f.write("\n")
        
        f.write(f"\n3. Sin和Cos分离:\n")
        f.write("-" * 40 + "\n")
        f.write("偶数维度 (sin函数):\n")
        sin_values = position_1_encoding[::2]
        for i in range(0, len(sin_values), 4):
            end_idx = min(i + 4, len(sin_values))
            dims = list(range(i*2, end_idx*2, 2))
            values = sin_values[i:end_idx]
            f.write(f"维度 {dims}: ")
            f.write(" ".join([f"{v:8.6f}" for v in values]))
            f.write("\n")
        
        f.write("\n奇数维度 (cos函数):\n")
        cos_values = position_1_encoding[1::2]
        for i in range(0, len(cos_values), 4):
            end_idx = min(i + 4, len(cos_values))
            dims = list(range(i*2+1, end_idx*2+1, 2))
            values = cos_values[i:end_idx]
            f.write(f"维度 {dims}: ")
            f.write(" ".join([f"{v:8.6f}" for v in values]))
            f.write("\n")
        
        f.write(f"\n4. 统计信息:\n")
        f.write("-" * 40 + "\n")
        f.write(f"最大值: {position_1_encoding.max():.6f} (维度 {np.argmax(position_1_encoding)})\n")
        f.write(f"最小值: {position_1_encoding.min():.6f} (维度 {np.argmin(position_1_encoding)})\n")
        f.write(f"平均值: {position_1_encoding.mean():.6f}\n")
        f.write(f"标准差: {position_1_encoding.std():.6f}\n")
        f.write(f"正值个数: {np.sum(position_1_encoding > 0)}\n")
        f.write(f"负值个数: {np.sum(position_1_encoding < 0)}\n")
        f.write(f"零值个数: {np.sum(position_1_encoding == 0)}\n")
        
        f.write(f"\n5. 数学公式验证 (前几个维度):\n")
        f.write("-" * 40 + "\n")
        pos = 1  # 位置1
        for i in range(4):
            div_term = math.exp(2 * i * (-math.log(10000.0) / d_model))
            sin_val = math.sin(pos * div_term)
            cos_val = math.cos(pos * div_term)
            
            f.write(f"i={i}, div_term={div_term:.8f}\n")
            f.write(f"  维度 {2*i:2d} (sin): 计算值={sin_val:.6f}, 实际值={position_1_encoding[2*i]:.6f}\n")
            f.write(f"  维度 {2*i+1:2d} (cos): 计算值={cos_val:.6f}, 实际值={position_1_encoding[2*i+1]:.6f}\n")
    
    print("位置1编码数值分析已保存到 result/position_1_encoding_analysis.txt")

def main():
    """主函数"""
    print("=" * 80)
    print("位置1编码值分析和可视化")
    print("=" * 80)
    
    # 1. 分析参数配置
    d_model, max_len, seq_len = analyze_position_encoding_parameters()
    
    print(f"\n回答你的问题:")
    print(f"📊 d_model = {d_model}")
    print(f"📊 pos总数 = {max_len} (最大支持), 实际使用 = {seq_len}")
    print(f"📊 位置含义: 0=Week, 1=Day, 2=Hour")
    
    # 2. 获取位置1的编码
    position_1_encoding = get_position_1_encoding(d_model)
    
    # 3. 绘制详细图表
    plot_position_1_encoding_detailed(position_1_encoding, d_model)
    
    # 4. 绘制简单图表（按要求格式）
    plot_position_1_simple(position_1_encoding, d_model)
    
    # 5. 创建数值分析
    create_numerical_analysis(position_1_encoding, d_model)
    
    print(f"\n" + "=" * 80)
    print("分析完成！")
    print(f"📊 d_model: {d_model}")
    print(f"📊 pos总数: {max_len} (最大支持)")
    print(f"📊 实际使用位置: {seq_len} (0=Week, 1=Day, 2=Hour)")
    print(f"\n生成的文件:")
    print(f"- result/position_1_encoding_simple.png (按要求格式的图表)")
    print(f"- result/position_1_encoding_detailed.png (详细分析图)")
    print(f"- result/position_1_encoding_analysis.txt (数值分析)")
    print("=" * 80)

if __name__ == "__main__":
    main()
