"""
测试GCN+FFT融合模型的完整性和正确性
"""
import torch
import numpy as np
import sys
import os

def test_fft_peak_predictor():
    """测试FFT高峰期预测器"""
    print("Testing FFT Peak Predictor...")
    try:
        from model.gcn_fft_fusion_model import FFTPeakPredictor
        
        device = torch.device('cpu')
        predictor = FFTPeakPredictor(time_steps=10, device=device)
        
        # 测试数据
        batch_size, station_num, time_steps = 2, 276, 10
        test_data = torch.randn(batch_size, station_num, time_steps)
        
        # 前向传播
        with torch.no_grad():
            output = predictor(test_data)
        
        print(f"  ✓ FFT Peak Predictor: {test_data.shape} -> {output.shape}")
        
        # 测试FFT特征提取
        fft_features = predictor.extract_fft_features(test_data)
        print(f"  ✓ FFT Feature Extraction: {test_data.shape} -> {fft_features.shape}")
        
        return True
    except Exception as e:
        print(f"  ✗ FFT Peak Predictor test failed: {e}")
        return False

def test_gcn_multiscale_predictor():
    """测试GCN多尺度预测器"""
    print("Testing GCN MultiScale Predictor...")
    try:
        from model.gcn_fft_fusion_model import GCNMultiScalePredictor
        
        device = torch.device('cpu')
        time_lag, pre_len, station_num = 10, 1, 276
        predictor = GCNMultiScalePredictor(time_lag, pre_len, station_num, device)
        
        # 测试数据
        batch_size = 2
        test_inflow = torch.randn(batch_size, station_num, time_lag * 3)
        test_adj = torch.randn(station_num, station_num)
        
        # 前向传播
        with torch.no_grad():
            output = predictor(test_inflow, test_adj)
        
        print(f"  ✓ GCN MultiScale Predictor: {test_inflow.shape} -> {output.shape}")
        
        return True
    except Exception as e:
        print(f"  ✗ GCN MultiScale Predictor test failed: {e}")
        return False

def test_fusion_model():
    """测试融合模型"""
    print("Testing GCN+FFT Fusion Model...")
    try:
        from model.gcn_fft_fusion_model import GCN_FFT_FusionModel
        
        device = torch.device('cpu')
        time_lag, pre_len, station_num, peak_time_steps = 10, 1, 276, 10
        model = GCN_FFT_FusionModel(time_lag, pre_len, station_num, device, peak_time_steps)
        
        # 测试数据
        batch_size = 2
        test_inflow = torch.randn(batch_size, station_num, time_lag * 3)
        test_adj = torch.randn(station_num, station_num)
        
        # 前向传播
        with torch.no_grad():
            output = model(test_inflow, test_adj)
        
        print(f"  ✓ Fusion Model forward pass: {test_inflow.shape} -> {output.shape}")
        
        # 测试高峰期检测
        peak_sequences = model.detect_peak_periods(test_inflow)
        print(f"  ✓ Peak period detection: {test_inflow.shape} -> {peak_sequences.shape}")
        
        # 测试模型信息
        model_info = model.get_model_info()
        print(f"  ✓ Model parameters: {model_info['total_parameters']:,}")
        
        return True
    except Exception as e:
        print(f"  ✗ Fusion Model test failed: {e}")
        return False

def test_dataloader_compatibility():
    """测试数据加载器兼容性"""
    print("Testing Dataloader Compatibility...")
    try:
        # 添加当前目录到Python路径
        import sys
        if '.' not in sys.path:
            sys.path.append('.')

        from data.get_inflow_only_dataloader import get_inflow_only_dataloader

        # 检查数据文件
        if not os.path.exists('./data/in_15min.csv'):
            print("  ✗ Data file './data/in_15min.csv' not found")
            return False

        # 测试数据加载
        train_loader, val_loader, test_loader, max_val, min_val = get_inflow_only_dataloader(
            time_interval=15, time_lag=10, tg_in_one_day=72,
            forecast_day_number=5, pre_len=1, batch_size=4
        )

        # 测试一个批次
        for X, Y in train_loader:
            print(f"  ✓ Data compatibility: X.shape={X.shape}, Y.shape={Y.shape}")
            break

        return True
    except Exception as e:
        print(f"  ✗ Dataloader compatibility test failed: {e}")
        return False

def test_model_components_integration():
    """测试模型组件集成"""
    print("Testing Model Components Integration...")
    try:
        from model.gcn_fft_fusion_model import GCN_FFT_FusionModel
        from utils.utils import GetLaplacian
        
        device = torch.device('cpu')
        time_lag, pre_len, station_num, peak_time_steps = 10, 1, 276, 10
        model = GCN_FFT_FusionModel(time_lag, pre_len, station_num, device, peak_time_steps)
        
        # 创建真实的邻接矩阵
        if os.path.exists('./data/adjacency.csv'):
            adjacency = np.loadtxt('./data/adjacency.csv', delimiter=",")
            adjacency = torch.tensor(GetLaplacian(adjacency).get_normalized_adj(station_num)).type(torch.float32)
            print(f"  ✓ Real adjacency matrix loaded: {adjacency.shape}")
        else:
            adjacency = torch.randn(station_num, station_num)
            print(f"  ⚠ Using random adjacency matrix: {adjacency.shape}")
        
        # 测试完整流程
        batch_size = 2
        test_inflow = torch.randn(batch_size, station_num, time_lag * 3)
        
        with torch.no_grad():
            # 分别测试各个组件
            gcn_output = model.gcn_predictor(test_inflow, adjacency)
            peak_sequences = model.detect_peak_periods(test_inflow)
            fft_output = model.fft_predictor(peak_sequences)
            fusion_output = model(test_inflow, adjacency)
        
        print(f"  ✓ GCN output: {gcn_output.shape}")
        print(f"  ✓ FFT output: {fft_output.shape}")
        print(f"  ✓ Fusion output: {fusion_output.shape}")
        
        return True
    except Exception as e:
        print(f"  ✗ Model components integration test failed: {e}")
        return False

def test_training_compatibility():
    """测试训练兼容性"""
    print("Testing Training Compatibility...")
    try:
        from model.gcn_fft_fusion_model import GCN_FFT_FusionModel
        
        device = torch.device('cpu')
        time_lag, pre_len, station_num, peak_time_steps = 10, 1, 276, 10
        model = GCN_FFT_FusionModel(time_lag, pre_len, station_num, device, peak_time_steps)
        
        # 测试优化器
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        mse_loss = torch.nn.MSELoss()
        
        # 模拟训练步骤
        batch_size = 2
        test_inflow = torch.randn(batch_size, station_num, time_lag * 3)
        test_target = torch.randn(batch_size, station_num, pre_len)
        test_adj = torch.randn(station_num, station_num)
        
        # 前向传播
        model.train()
        prediction = model(test_inflow, test_adj)
        loss = mse_loss(prediction, test_target)
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        print(f"  ✓ Training step completed, loss: {loss.item():.6f}")
        
        return True
    except Exception as e:
        print(f"  ✗ Training compatibility test failed: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 80)
    print("GCN+FFT Fusion Model Test Suite")
    print("=" * 80)
    
    tests = [
        ("FFT Peak Predictor", test_fft_peak_predictor),
        ("GCN MultiScale Predictor", test_gcn_multiscale_predictor),
        ("Fusion Model", test_fusion_model),
        ("Dataloader Compatibility", test_dataloader_compatibility),
        ("Model Components Integration", test_model_components_integration),
        ("Training Compatibility", test_training_compatibility)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 50)
        result = test_func()
        results.append((test_name, result))
    
    # 总结
    print("\n" + "=" * 80)
    print("Test Results Summary:")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"  {test_name:<30}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The GCN+FFT Fusion model is ready to use.")
        print("\nArchitecture Summary:")
        print("  ✅ GCN: 多尺度客流预测 (实时/日/周)")
        print("  ✅ FFT: 高峰期频域预测 (10个时间步)")
        print("  ✅ Fusion: 自适应权重融合")
        print("\nNext steps:")
        print("  1. Run 'python main_gcn_fft_fusion.py' to train the fusion model")
        print("  2. Run 'python predict_gcn_fft_fusion.py' to make predictions")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix the issues before proceeding.")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
