"""
测试MLP模型的完整性和正确性
"""
import torch
import numpy as np
import sys
import os

# 添加当前目录到Python路径
if '.' not in sys.path:
    sys.path.append('.')

def test_mlp_model():
    """测试MLP模型"""
    print("Testing MLP Model...")
    try:
        from model.mlp_model import MLPPredictor
        
        device = torch.device('cpu')
        time_lag, pre_len, station_num = 10, 1, 276
        model = MLPPredictor(time_lag, pre_len, station_num, device=device)
        
        # 测试数据
        batch_size = 2
        test_data = torch.randn(batch_size, station_num, time_lag * 3)
        
        # 前向传播
        with torch.no_grad():
            output = model(test_data)
        
        print(f"  ✓ MLP Model: {test_data.shape} -> {output.shape}")
        
        # 检查输出维度
        expected_shape = (batch_size, station_num, pre_len)
        if output.shape == expected_shape:
            print(f"  ✓ Output shape correct: {output.shape}")
            
            # 测试模型信息
            model_info = model.get_model_info()
            print(f"  ✓ Model parameters: {model_info['total_parameters']:,}")
            return True
        else:
            print(f"  ✗ Output shape incorrect: expected {expected_shape}, got {output.shape}")
            return False
        
    except Exception as e:
        print(f"  ✗ MLP Model test failed: {e}")
        return False

def test_data_compatibility():
    """测试数据兼容性"""
    print("Testing Data Compatibility...")
    try:
        # 检查数据文件
        if not os.path.exists('./data/in_15min.csv'):
            print("  ✗ Data file './data/in_15min.csv' not found")
            return False
        
        from data.get_inflow_only_dataloader import get_inflow_only_dataloader
        
        # 测试数据加载
        train_loader, val_loader, test_loader, max_val, min_val = get_inflow_only_dataloader(
            time_interval=15, time_lag=10, tg_in_one_day=72, 
            forecast_day_number=5, pre_len=1, batch_size=4
        )
        
        # 测试一个批次
        for X, Y in train_loader:
            print(f"  ✓ Data compatibility: X.shape={X.shape}, Y.shape={Y.shape}")
            break
        
        return True
    except Exception as e:
        print(f"  ✗ Data compatibility test failed: {e}")
        return False

def test_training_compatibility():
    """测试训练兼容性"""
    print("Testing Training Compatibility...")
    try:
        from model.mlp_model import MLPPredictor
        
        device = torch.device('cpu')
        time_lag, pre_len, station_num = 10, 1, 276
        
        # 测试MLP训练
        model = MLPPredictor(time_lag, pre_len, station_num, device=device)
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        mse_loss = torch.nn.MSELoss()
        
        # 模拟训练数据
        batch_size = 2
        train_X = torch.randn(batch_size, station_num, time_lag * 3)
        train_Y = torch.randn(batch_size, station_num, pre_len)
        
        # 训练步骤
        model.train()
        pred = model(train_X)
        loss = mse_loss(pred, train_Y)
        
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        print(f"  ✓ MLP training step completed, loss: {loss.item():.6f}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Training compatibility test failed: {e}")
        return False

def test_metrics_calculation():
    """测试指标计算"""
    print("Testing Metrics Calculation...")
    try:
        from main_mlp import calculate_metrics
        
        # 创建测试数据
        predictions = np.random.randn(100, 276, 1)
        true_values = np.random.randn(100, 276, 1)
        
        # 计算指标
        rmse, mae, wmape = calculate_metrics(predictions, true_values)
        
        print(f"  ✓ Metrics calculated: RMSE={rmse:.4f}, MAE={mae:.4f}, WMAPE={wmape:.4f}%")
        
        # 验证指标合理性
        if rmse > 0 and mae > 0 and wmape >= 0:
            print(f"  ✓ Metrics values are reasonable")
            return True
        else:
            print(f"  ✗ Metrics values are unreasonable")
            return False
        
    except Exception as e:
        print(f"  ✗ Metrics calculation test failed: {e}")
        return False

def test_early_stopping():
    """测试早停机制"""
    print("Testing Early Stopping...")
    try:
        from utils.earlystopping import EarlyStopping
        
        # 创建早停实例，patience=20
        early_stopping = EarlyStopping(patience=20, verbose=False)
        
        # 检查patience设置
        if early_stopping.patience == 20:
            print(f"  ✓ Early stopping patience set to 20")
        else:
            print(f"  ✗ Early stopping patience incorrect: {early_stopping.patience}")
            return False
        
        # 检查属性
        if hasattr(early_stopping, 'best_model_path'):
            print(f"  ✓ Early stopping has best_model_path attribute")
            return True
        else:
            print(f"  ✗ Early stopping missing best_model_path attribute")
            return False
        
    except Exception as e:
        print(f"  ✗ Early stopping test failed: {e}")
        return False

def test_model_architecture():
    """测试模型架构"""
    print("Testing Model Architecture...")
    try:
        from model.mlp_model import MLPPredictor
        
        device = torch.device('cpu')
        time_lag, pre_len, station_num = 10, 1, 276
        
        # 测试不同的隐藏层配置
        hidden_configs = [
            [512, 256, 128],  # 默认配置
            [256, 128],       # 较小配置
            [1024, 512, 256, 128]  # 较大配置
        ]
        
        for i, hidden_dims in enumerate(hidden_configs):
            model = MLPPredictor(time_lag, pre_len, station_num, hidden_dims=hidden_dims, device=device)
            
            # 测试前向传播
            test_input = torch.randn(2, station_num, time_lag * 3)
            with torch.no_grad():
                output = model(test_input)
            
            model_info = model.get_model_info()
            print(f"  ✓ Config {i+1}: {len(hidden_dims)} layers, {model_info['total_parameters']:,} params")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Model architecture test failed: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 80)
    print("MLP Model Test Suite")
    print("=" * 80)
    
    tests = [
        ("MLP Model", test_mlp_model),
        ("Data Compatibility", test_data_compatibility),
        ("Training Compatibility", test_training_compatibility),
        ("Metrics Calculation", test_metrics_calculation),
        ("Early Stopping", test_early_stopping),
        ("Model Architecture", test_model_architecture)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 50)
        result = test_func()
        results.append((test_name, result))
    
    # 总结
    print("\n" + "=" * 80)
    print("Test Results Summary:")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"  {test_name:<25}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The MLP system is ready to use.")
        print("\nModel Features:")
        print("  ✅ Simple MLP architecture with 3 hidden layers")
        print("  ✅ Batch normalization and dropout for regularization")
        print("  ✅ Early stopping with patience = 20")
        print("  ✅ Learning rate scheduling")
        print("  ✅ Gradient clipping for stable training")
        print("\nNext steps:")
        print("  1. Run 'python main_mlp.py' to train and evaluate the MLP model")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix the issues before proceeding.")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
