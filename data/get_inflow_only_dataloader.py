from data.datasets import Traffic_inflow
from torch.utils.data import DataLoader


inflow_data = "./data/in_15min.csv"


def get_inflow_only_dataloader(time_interval=15, time_lag=10, tg_in_one_day=72, 
                               forecast_day_number=5, pre_len=1, batch_size=32):
    """
    获取只包含inflow数据的数据加载器
    
    Args:
        time_interval: 时间间隔（分钟）
        time_lag: 时间滞后步数
        tg_in_one_day: 一天中的时间粒度数量
        forecast_day_number: 预测天数
        pre_len: 预测长度
        batch_size: 批次大小
    
    Returns:
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器  
        test_loader: 测试数据加载器
        max_inflow: 最大inflow值
        min_inflow: 最小inflow值
    """
    
    # 训练数据加载器
    print("Loading train inflow data...")
    inflow_train = Traffic_inflow(
        time_interval=time_interval, 
        time_lag=time_lag, 
        tg_in_one_day=tg_in_one_day, 
        forecast_day_number=forecast_day_number,
        pre_len=pre_len, 
        inflow_data=inflow_data, 
        is_train=True, 
        is_val=False, 
        val_rate=0.1
    )
    max_inflow, min_inflow = inflow_train.get_max_min_inflow()
    inflow_data_loader_train = DataLoader(inflow_train, batch_size=batch_size, shuffle=True)

    # 验证数据加载器
    print("Loading validation inflow data...")
    inflow_val = Traffic_inflow(
        time_interval=time_interval, 
        time_lag=time_lag, 
        tg_in_one_day=tg_in_one_day, 
        forecast_day_number=forecast_day_number,
        pre_len=pre_len, 
        inflow_data=inflow_data, 
        is_train=True, 
        is_val=True, 
        val_rate=0.1
    )
    inflow_data_loader_val = DataLoader(inflow_val, batch_size=batch_size, shuffle=False)

    # 测试数据加载器
    print("Loading test inflow data...")
    inflow_test = Traffic_inflow(
        time_interval=time_interval, 
        time_lag=time_lag, 
        tg_in_one_day=tg_in_one_day, 
        forecast_day_number=forecast_day_number,
        pre_len=pre_len, 
        inflow_data=inflow_data, 
        is_train=False, 
        is_val=False, 
        val_rate=0
    )
    inflow_data_loader_test = DataLoader(inflow_test, batch_size=batch_size, shuffle=False)

    print(f"Data loading completed:")
    print(f"  - Train batches: {len(inflow_data_loader_train)}")
    print(f"  - Validation batches: {len(inflow_data_loader_val)}")
    print(f"  - Test batches: {len(inflow_data_loader_test)}")
    print(f"  - Max inflow: {max_inflow}")
    print(f"  - Min inflow: {min_inflow}")

    return inflow_data_loader_train, inflow_data_loader_val, inflow_data_loader_test, max_inflow, min_inflow


def test_dataloader():
    """测试数据加载器功能"""
    print("Testing inflow-only dataloader...")
    
    train_loader, val_loader, test_loader, max_val, min_val = get_inflow_only_dataloader(
        time_interval=15, 
        time_lag=10, 
        tg_in_one_day=72, 
        forecast_day_number=5, 
        pre_len=1, 
        batch_size=8
    )
    
    # 测试训练数据
    for i, (X, Y) in enumerate(train_loader):
        print(f"Train batch {i}: X.shape = {X.shape}, Y.shape = {Y.shape}")
        if i >= 2:  # 只测试前3个批次
            break
    
    # 测试验证数据
    for i, (X, Y) in enumerate(val_loader):
        print(f"Val batch {i}: X.shape = {X.shape}, Y.shape = {Y.shape}")
        if i >= 1:  # 只测试前2个批次
            break
    
    # 测试测试数据
    for i, (X, Y, Y_orig) in enumerate(test_loader):
        print(f"Test batch {i}: X.shape = {X.shape}, Y.shape = {Y.shape}, Y_orig.shape = {Y_orig.shape}")
        if i >= 1:  # 只测试前2个批次
            break
    
    print("Dataloader test completed successfully!")


if __name__ == "__main__":
    test_dataloader()
