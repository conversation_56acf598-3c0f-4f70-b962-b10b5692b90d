import numpy as np
import os
import time
import torch
from torch import nn
import sys

# 添加当前目录到Python路径
if '.' not in sys.path:
    sys.path.append('.')

# 可选导入
try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_AVAILABLE = True
except ImportError:
    print("Warning: TensorBoard not available. Logging will be disabled.")
    TENSORBOARD_AVAILABLE = False

try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    print("Warning: Matplotlib not available. Visualization will be disabled.")
    MATPLOTLIB_AVAILABLE = False

# 导入模块
try:
    from model.mlp_model import MLPPredictor
    from data.get_inflow_only_dataloader import get_inflow_only_dataloader
    from utils.earlystopping import EarlyStopping
    print("All modules imported successfully!")
except ImportError as e:
    print(f"Import error: {e}")
    print("Please check if all required files exist in the correct directories.")
    exit(1)


def calculate_metrics(predictions, true_values):
    """计算评估指标"""
    # 确保数据为numpy数组
    if isinstance(predictions, torch.Tensor):
        predictions = predictions.cpu().numpy()
    if isinstance(true_values, torch.Tensor):
        true_values = true_values.cpu().numpy()

    # 展平数据
    pred_flat = predictions.reshape(-1)
    true_flat = true_values.reshape(-1)

    # 确保非负值和避免除零
    pred_flat = np.maximum(pred_flat, 0)
    true_flat = np.maximum(true_flat, 0)

    # 计算指标
    rmse = np.sqrt(np.mean((pred_flat - true_flat) ** 2))
    mae = np.mean(np.abs(pred_flat - true_flat))

    # 计算WMAPE，避免除零
    total_true = np.sum(true_flat)
    if total_true > 0:
        wmape = np.sum(np.abs(true_flat - pred_flat)) / total_true * 100
    else:
        wmape = 0.0

    return rmse, mae, wmape


def train_mlp_model(device, time_lag, pre_len, station_num, train_loader, val_loader, 
                   max_epochs=500, lr=0.001, save_dir='./save_model/mlp'):
    """训练MLP模型"""
    print("\n" + "=" * 80)
    print("Training MLP Model...")
    print("=" * 80)
    
    # 创建MLP模型
    model = MLPPredictor(
        time_lag=time_lag, 
        pre_len=pre_len, 
        station_num=station_num, 
        hidden_dims=[512, 256, 128],  # 三层隐藏层
        dropout=0.3,
        device=device
    )
    model = model.to(device)
    
    # 打印模型信息
    model_info = model.get_model_info()
    print("Model Information:")
    for key, value in model_info.items():
        print(f"  {key}: {value}")
    
    # 优化器和损失函数
    optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=10, factor=0.5)
    mse_loss = torch.nn.MSELoss().to(device)
    
    # 早停机制 - 设置为20
    early_stopping = EarlyStopping(patience=20, verbose=True)
    
    # TensorBoard
    if TENSORBOARD_AVAILABLE:
        writer = SummaryWriter(log_dir=f'./runs/mlp_{time.strftime("%Y%m%d_%H%M%S")}')
    else:
        writer = None
    
    # 训练循环
    train_losses = []
    val_losses = []
    
    print(f"Starting training with early stopping patience = 20...")
    
    for epoch in range(max_epochs):
        # 训练阶段
        model.train()
        train_loss = 0
        train_batches = 0
        
        for i_batch, (train_X, train_Y) in enumerate(train_loader):
            train_X = train_X.type(torch.float32).to(device)
            train_Y = train_Y.type(torch.float32).to(device)
            
            # 前向传播
            prediction = model(train_X)
            loss = mse_loss(prediction, train_Y)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            train_loss += loss.item()
            train_batches += 1
        
        # 验证阶段
        model.eval()
        val_loss = 0
        val_batches = 0
        
        with torch.no_grad():
            for i_batch, (val_X, val_Y) in enumerate(val_loader):
                val_X = val_X.type(torch.float32).to(device)
                val_Y = val_Y.type(torch.float32).to(device)
                
                prediction = model(val_X)
                loss = mse_loss(prediction, val_Y)
                
                val_loss += loss.item()
                val_batches += 1
        
        # 计算平均损失
        avg_train_loss = train_loss / train_batches
        avg_val_loss = val_loss / val_batches
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        # 记录到TensorBoard
        if writer is not None:
            writer.add_scalar("Loss/Train", avg_train_loss, epoch)
            writer.add_scalar("Loss/Validation", avg_val_loss, epoch)
            writer.add_scalar("Learning_Rate", optimizer.param_groups[0]['lr'], epoch)
        
        # 打印训练信息
        if epoch % 10 == 0 or epoch < 10:
            current_lr = optimizer.param_groups[0]['lr']
            print(f'Epoch {epoch:4d}: Train Loss = {avg_train_loss:.6f}, Val Loss = {avg_val_loss:.6f}, LR = {current_lr:.6f}')
        
        # 早停检查
        if epoch > 0:
            model_dict = model.state_dict()
            early_stopping(avg_val_loss, model_dict, model, epoch, save_dir)
            if early_stopping.early_stop:
                print(f"Early stopping at epoch {epoch}")
                break
    
    # 关闭TensorBoard
    if writer is not None:
        writer.close()
    
    print("MLP training completed!")
    
    # 确保返回正确的模型路径
    best_model_path = getattr(early_stopping, 'best_model_path', None)
    if best_model_path is None:
        # 如果没有保存的模型路径，创建一个默认路径
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        best_model_path = f"{save_dir}/mlp_final_{timestamp}.pth"
        torch.save(model.state_dict(), best_model_path)
        print(f"Saved final model to: {best_model_path}")
    
    return model, best_model_path


def evaluate_mlp_model(model, test_loader, device, max_inflow, min_inflow, station_num):
    """评估MLP模型并生成可视化"""
    print("\n" + "=" * 80)
    print("MLP Model Evaluation...")
    print("=" * 80)
    
    model.eval()
    
    predictions = []
    true_values = []
    true_values_original = []
    
    with torch.no_grad():
        for i_batch, (test_X, test_Y, test_Y_orig) in enumerate(test_loader):
            test_X = test_X.type(torch.float32).to(device)
            test_Y = test_Y.type(torch.float32).to(device)
            
            # MLP预测
            prediction = model(test_X)
            
            # 保存预测结果
            predictions.extend(prediction.cpu().numpy())
            true_values.extend(test_Y.cpu().numpy())
            true_values_original.extend(test_Y_orig.numpy())
    
    # 转换为numpy数组
    predictions = np.array(predictions)
    true_values = np.array(true_values)
    true_values_original = np.array(true_values_original)
    
    # 反归一化
    predictions_original = predictions * (max_inflow - min_inflow) + min_inflow
    
    # 确保非负值
    predictions_original = np.maximum(predictions_original, 0)
    true_values_original = np.maximum(true_values_original, 0)
    
    # 计算评估指标
    rmse, mae, wmape = calculate_metrics(predictions_original, true_values_original)
    
    print(f"MLP Model Performance:")
    print(f"  RMSE: {rmse:.4f}")
    print(f"  MAE:  {mae:.4f}")
    print(f"  WMAPE: {wmape:.4f}%")
    
    # 保存结果
    result_dir = 'result'
    os.makedirs(result_dir, exist_ok=True)
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    
    np.savetxt(f'{result_dir}/mlp_predictions_{timestamp}.txt', predictions_original.reshape(-1, station_num), fmt='%.2f')
    np.savetxt(f'{result_dir}/mlp_true_values_{timestamp}.txt', true_values_original.reshape(-1, station_num), fmt='%.2f')
    
    # 保存评估指标
    with open(f'{result_dir}/mlp_metrics_{timestamp}.txt', 'w') as f:
        f.write("MLP Model Evaluation Metrics\n")
        f.write("=" * 40 + "\n")
        f.write(f"RMSE: {rmse:.4f}\n")
        f.write(f"MAE: {mae:.4f}\n")
        f.write(f"WMAPE: {wmape:.4f}%\n")
        f.write(f"Data Shape: {predictions_original.shape}\n")
        f.write(f"Total Predictions: {predictions_original.size}\n")
    
    # 可视化关键车站预测对比
    if MATPLOTLIB_AVAILABLE:
        key_stations = [0, 50, 100, 150, 200]  # 选择几个关键车站
        num_samples = min(50, len(true_values_original))
        
        plt.figure(figsize=(15, 10))
        for i, station_idx in enumerate(key_stations):
            plt.subplot(2, 3, i+1)
            plt.plot(true_values_original[:num_samples, station_idx, 0], 'b-', label='True', linewidth=2)
            plt.plot(predictions_original[:num_samples, station_idx, 0], 'r--', label='MLP Prediction', linewidth=2)
            plt.title(f'Station {station_idx} - MLP Prediction')
            plt.xlabel('Time Steps')
            plt.ylabel('Flow')
            plt.legend()
            plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plot_file = f'{result_dir}/mlp_comparison_{timestamp}.png'
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        print(f"Visualization saved: {plot_file}")
    
    return {'rmse': rmse, 'mae': mae, 'wmape': wmape}


def main():
    """主函数"""
    # 设备配置
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # 模型参数配置
    time_interval = 15
    time_lag = 10
    tg_in_one_day = 72
    forecast_day_number = 5
    pre_len = 1
    batch_size = 32
    station_num = 276
    
    print("=" * 80)
    print("MLP Metro Flow Prediction System")
    print("基于MLP的地铁客流预测系统")
    print("=" * 80)
    print(f"Configuration:")
    print(f"  - Time Lag: {time_lag}")
    print(f"  - Prediction Length: {pre_len}")
    print(f"  - Station Number: {station_num}")
    print(f"  - Batch Size: {batch_size}")
    print(f"  - Early Stopping Patience: 20")
    print("=" * 80)
    
    # 加载数据
    print("Loading data...")
    train_loader, val_loader, test_loader, max_inflow, min_inflow = get_inflow_only_dataloader(
        time_interval=time_interval, 
        time_lag=time_lag, 
        tg_in_one_day=tg_in_one_day, 
        forecast_day_number=forecast_day_number, 
        pre_len=pre_len, 
        batch_size=batch_size
    )
    
    # 创建保存目录
    timestamp = time.strftime("%Y_%m_%d_%H_%M_%S")
    save_dir = f'./save_model/mlp_{timestamp}'
    os.makedirs(save_dir, exist_ok=True)
    
    # 训练MLP模型
    model, model_path = train_mlp_model(
        device, time_lag, pre_len, station_num, train_loader, val_loader,
        max_epochs=500, lr=0.001, save_dir=save_dir
    )
    
    # 评估模型
    results = evaluate_mlp_model(
        model, test_loader, device, max_inflow, min_inflow, station_num
    )
    
    print("\n" + "=" * 80)
    print("MLP Training and Evaluation Completed!")
    print("=" * 80)
    print("Final Results:")
    print(f"  RMSE:  {results['rmse']:.4f}")
    print(f"  MAE:   {results['mae']:.4f}")
    print(f"  WMAPE: {results['wmape']:.4f}%")
    print("=" * 80)
    print(f"Model saved: {model_path}")
    print("Results and visualizations saved in 'result/' directory")


if __name__ == "__main__":
    main()
