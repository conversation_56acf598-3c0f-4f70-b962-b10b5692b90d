"""
简单的导入验证脚本
"""
import sys

# 添加当前目录到Python路径
if '.' not in sys.path:
    sys.path.append('.')

def verify_imports():
    """验证所有必要的导入"""
    print("Verifying imports for GCN+FFT Fusion system...")
    
    try:
        # 基础导入
        import numpy as np
        import torch
        from torch import nn
        print("✓ Basic modules imported")
        
        # 工具导入
        from utils.utils import GetLaplacian
        from utils.earlystopping import EarlyStopping
        print("✓ Utility modules imported")
        
        # 数据导入
        from data.get_inflow_only_dataloader import get_inflow_only_dataloader
        from data.datasets import Traffic_inflow
        print("✓ Data modules imported")
        
        # 模型导入
        from model.gcn_fft_fusion_model import GCN_FFT_FusionModel
        from model.GCN_layers import GraphConvolution
        print("✓ Model modules imported")
        
        # 测试模型创建
        device = torch.device('cpu')
        model = GCN_FFT_FusionModel(10, 1, 276, device, 10)
        print(f"✓ Model created with {sum(p.numel() for p in model.parameters()):,} parameters")
        
        print("\n🎉 All imports verified successfully!")
        print("Your GCN+FFT Fusion system is ready to use!")
        
        return True
        
    except Exception as e:
        print(f"❌ Import verification failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    verify_imports()
