#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
绘制车站18,232在测试集上五天高峰期共50个时间步的原始客流数据

Author: 008 - AI Algorithm Engineer for Subway Flow Prediction
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import os

# 设置matplotlib后端和中文字体
matplotlib.use('Agg')
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_main_model_results():
    """加载主模型的真实值数据"""
    print("正在加载主模型的真实值数据...")
    
    try:
        # 加载真实值数据
        main_true_values = np.loadtxt('result/improved_ALL_276_stations_original.txt')
        
        print(f"真实值数据形状: {main_true_values.shape}")
        return main_true_values
        
    except Exception as e:
        print(f"加载真实值数据失败: {e}")
        return None

def extract_peak_sequences_for_stations(station_ids, main_true_values):
    """为指定车站提取高峰期序列数据"""
    print("正在提取指定车站的高峰期序列数据...")
    
    # 加载高峰时刻信息
    peak_times = np.loadtxt('./data/continuous_peak_flow_dataset_peak_times.csv', delimiter=',', dtype=int)
    
    # 假设测试集有5天数据，每天72个时间步，高峰期在每天的某个固定时刻
    test_days = 5
    steps_per_day = 72
    train_val_days = 20  # 前20天为训练+验证集
    
    station_data = {}
    
    for station_id in station_ids:
        print(f"处理车站 {station_id}...")
        
        # 获取该车站的高峰时刻
        peak_time = peak_times[station_id]
        
        # 提取5天的高峰序列，每天10个时间步
        true_sequences = []
        
        for day in range(test_days):
            # 计算该天的高峰时刻在测试集中的位置
            day_start = day * steps_per_day
            peak_start = day_start + peak_time - 4  # 高峰时刻前4步
            peak_end = peak_start + 10  # 10个时间步
            
            # 确保索引在有效范围内
            peak_start = max(0, min(peak_start, main_true_values.shape[1] - 10))
            peak_end = peak_start + 10
            
            true_sequences.extend(main_true_values[station_id, peak_start:peak_end])
        
        true_seq = np.array(true_sequences)
        
        # 确保序列长度为50
        if len(true_seq) > 50:
            true_seq = true_seq[:50]
        elif len(true_seq) < 50:
            # 填充到50个点
            padding_length = 50 - len(true_seq)
            true_seq = np.pad(true_seq, (0, padding_length), 'edge')
        
        station_data[station_id] = {
            'true_values': true_seq,
            'peak_time': peak_time
        }
        
        print(f"车站{station_id}: 高峰时刻{peak_time//4:02d}:{(peak_time%4)*15:02d}, 提取了{len(true_seq)}个时间步")
    
    return station_data

def plot_original_data(station_data, station_ids):
    """绘制原始客流数据"""
    print("正在绘制原始客流数据...")

    # 创建1x2的子图布局
    fig, axes = plt.subplots(1, 2, figsize=(16, 6))

    # 横坐标范围0-50
    x_axis = range(50)

    # 定义车站标签映射
    station_labels = ['E', 'F']

    for i, station_id in enumerate(station_ids):
        ax = axes[i]
        data = station_data[station_id]

        # 获取数据
        true_values = data['true_values']
        peak_time = data['peak_time']

        # 计算统计信息
        avg_flow = np.mean(true_values)
        min_flow = np.min(true_values)
        max_flow = np.max(true_values)
        std_flow = np.std(true_values)

        # 绘制原始客流数据曲线
        ax.plot(x_axis, true_values, 'b-', linewidth=2.5, alpha=0.8, marker='o', markersize=4, label='Original Flow Data')

        # 添加平均值线
      #  ax.axhline(y=avg_flow, color='r', linestyle='--', alpha=0.7, linewidth=1.5, label=f'Average: {avg_flow:.0f}')

        # 设置标题和标签 - 使用E、F标签
        hour = peak_time // 4
        minute = (peak_time % 4) * 15

        ax.set_title(f'Station {station_labels[i]} - Original Flow Data\n',
                    fontsize=14, fontweight='bold')
        ax.set_xlabel('Time Steps', fontsize=12)
        ax.set_ylabel('Passenger Flow', fontsize=12)
        ax.legend(fontsize=10, loc='upper right')
        ax.grid(True, alpha=0.3)
        
        # 设置横坐标范围
        ax.set_xlim(0, 49)
        
        # 设置纵坐标范围，留出一些边距
        y_range = max_flow - min_flow
        ax.set_ylim(min_flow - y_range*0.1, max_flow + y_range*0.1)
        
        # 添加统计信息
      #  stats_text = f'Statistics:\nAvg: {avg_flow:.0f}\nMin: {min_flow:.0f}\nMax: {max_flow:.0f}\nStd: {std_flow:.0f}'
       # ax.text(0.02, 0.98, stats_text,
        #        transform=ax.transAxes, fontsize=10, verticalalignment='top',
         #       bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.7))
        
        # 标记每天的分界线
        for day in range(1, 5):
            day_boundary = day * 10
            ax.axvline(x=day_boundary, color='gray', linestyle=':', alpha=0.5, linewidth=1)
            ax.text(day_boundary, max_flow + y_range*0.05, f'Day {day+1}', 
                   ha='left', va='bottom', fontsize=8, color='gray')
        
        # 标记第一天
        ax.text(5, max_flow + y_range*0.05, 'Day 1', 
               ha='center', va='bottom', fontsize=8, color='gray')
    
    plt.tight_layout()
    
    # 确保result目录存在
    if not os.path.exists('result'):
        os.makedirs('result')
    
    plt.savefig('result/stations_18_232_original_flow_data.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("原始客流数据图已保存到 result/stations_18_232_original_flow_data.png")

def create_data_summary(station_data, station_ids):
    """创建数据汇总表"""
    print("正在创建数据汇总表...")
    
    with open('result/stations_18_232_original_data_summary.txt', 'w', encoding='utf-8') as f:
        f.write("车站18,232原始客流数据汇总\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("数据说明:\n")
        f.write("- 数据来源: 测试集后5天高峰期10个时间步\n")
        f.write("- 时间范围: 0-50 (5天×10时间步)\n")
        f.write("- 数据类型: 原始客流数据\n\n")
        
        f.write("各车站数据统计:\n")
        f.write("-" * 60 + "\n")
        f.write(f"{'车站ID':<8} {'高峰时刻':<8} {'平均值':<8} {'最小值':<8} {'最大值':<8} {'标准差':<8}\n")
        f.write("-" * 60 + "\n")
        
        for station_id in station_ids:
            if station_id in station_data:
                data = station_data[station_id]
                true_values = data['true_values']
                peak_time = data['peak_time']
                
                hour = peak_time // 4
                minute = (peak_time % 4) * 15
                peak_time_str = f"{hour:02d}:{minute:02d}"
                
                avg_flow = np.mean(true_values)
                min_flow = np.min(true_values)
                max_flow = np.max(true_values)
                std_flow = np.std(true_values)
                
                f.write(f"{station_id:<8} {peak_time_str:<8} {avg_flow:<8.0f} {min_flow:<8.0f} {max_flow:<8.0f} {std_flow:<8.0f}\n")
        
        f.write("\n详细数据序列:\n")
        f.write("-" * 60 + "\n")
        
        for station_id in station_ids:
            if station_id in station_data:
                data = station_data[station_id]
                true_values = data['true_values']
                peak_time = data['peak_time']
                
                hour = peak_time // 4
                minute = (peak_time % 4) * 15
                
                f.write(f"\n车站{station_id} (高峰时刻: {hour:02d}:{minute:02d}):\n")
                
                # 按天分组显示数据
                for day in range(5):
                    start_idx = day * 10
                    end_idx = start_idx + 10
                    day_data = true_values[start_idx:end_idx]
                    
                    f.write(f"  Day {day+1}: ")
                    for j, value in enumerate(day_data):
                        f.write(f"{value:6.0f}")
                        if j < len(day_data) - 1:
                            f.write(", ")
                    f.write(f" (Avg: {np.mean(day_data):.0f})\n")
    
    print("数据汇总表已保存到 result/stations_18_232_original_data_summary.txt")

def main():
    """主函数"""
    print("=" * 60)
    print("绘制车站18,232原始客流数据")
    print("测试集后5天高峰期共50个时间步")
    print("=" * 60)
    
    # 指定的2个车站
    station_ids = [18, 232]
    
    # 1. 加载真实值数据
    main_true_values = load_main_model_results()
    
    if main_true_values is None:
        print("无法加载真实值数据，退出程序")
        return
    
    # 2. 提取指定车站的高峰期序列
    station_data = extract_peak_sequences_for_stations(station_ids, main_true_values)
    
    # 3. 绘制原始客流数据
    plot_original_data(station_data, station_ids)
    
    # 4. 创建数据汇总
    create_data_summary(station_data, station_ids)
    
    # 5. 输出简要结果
    print("\n各车站原始数据统计:")
    print("-" * 50)
    print(f"{'车站ID':<8} {'高峰时刻':<8} {'平均值':<8} {'最小值':<8} {'最大值':<8}")
    print("-" * 50)
    
    for station_id in station_ids:
        if station_id in station_data:
            data = station_data[station_id]
            true_values = data['true_values']
            peak_time = data['peak_time']
            
            hour = peak_time // 4
            minute = (peak_time % 4) * 15
            peak_time_str = f"{hour:02d}:{minute:02d}"
            
            avg_flow = np.mean(true_values)
            min_flow = np.min(true_values)
            max_flow = np.max(true_values)
            
            print(f"{station_id:<8} {peak_time_str:<8} {avg_flow:<8.0f} {min_flow:<8.0f} {max_flow:<8.0f}")
    
    print("\n绘制完成！")
    print("生成文件:")
    print("- result/stations_18_232_original_flow_data.png (原始客流数据图)")
    print("- result/stations_18_232_original_data_summary.txt (数据汇总表)")
    print("=" * 60)

if __name__ == "__main__":
    main()
