"""
Transformer+FFT地铁客流预测系统演示脚本
展示Transformer完整预测 + FFT高峰期预测的融合架构
"""
import torch
import numpy as np
import time
import sys

# 添加当前目录到Python路径
if '.' not in sys.path:
    sys.path.append('.')

try:
    from model.transformer_fft_model import TransformerFFTFusionModel, TransformerPredictor, FFTPeakPredictor
    from data.get_inflow_only_dataloader import get_inflow_only_dataloader
    from main_transformer_fft import calculate_metrics
    print("All modules imported successfully!")
except ImportError as e:
    print(f"Import error: {e}")
    exit(1)

def demo_architecture():
    """演示架构的各个组件"""
    print("=" * 80)
    print("Transformer+FFT 地铁客流预测系统 - 架构演示")
    print("=" * 80)
    
    # 设备配置
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 模型参数
    time_lag = 10
    pre_len = 1
    station_num = 276
    peak_time_steps = 10
    batch_size = 4
    
    print(f"\n模型配置:")
    print(f"  - 时间滞后: {time_lag}")
    print(f"  - 预测长度: {pre_len}")
    print(f"  - 车站数量: {station_num}")
    print(f"  - FFT高峰期时间步: {peak_time_steps}")
    print(f"  - 批次大小: {batch_size}")
    
    # 创建融合模型
    print("正在创建Transformer+FFT融合模型...")
    fusion_model = TransformerFFTFusionModel(time_lag, pre_len, station_num, device, peak_time_steps)
    fusion_model = fusion_model.to(device)
    
    # 创建独立的Transformer和FFT模型用于对比
    transformer_model = TransformerPredictor(time_lag, pre_len, station_num, device=device)
    fft_model = FFTPeakPredictor(peak_time_steps, station_num, device=device)
    
    # 打印模型信息
    fusion_info = fusion_model.get_model_info()
    print(f"\n融合模型信息:")
    print(f"  - 总参数量: {fusion_info['total_parameters']:,}")
    print(f"  - 可训练参数: {fusion_info['trainable_parameters']:,}")
    
    # 创建测试数据
    test_inflow = torch.randn(batch_size, time_lag * 3, station_num).to(device)
    
    print(f"\n" + "=" * 80)
    print("组件功能演示")
    print("=" * 80)
    
    with torch.no_grad():
        # 1. Transformer预测演示
        print(f"\n1. Transformer预测器:")
        print(f"   - 处理: 完整的时间序列预测")
        transformer_output = transformer_model(test_inflow)
        print(f"   - 输入: {test_inflow.shape}")
        print(f"   - 输出: {transformer_output.shape}")
        print(f"   - 功能: 基于注意力机制的序列到序列预测")
        
        # 2. FFT高峰期预测演示
        print(f"\n2. FFT高峰期预测器:")
        print(f"   - 处理: 高峰期10个时间步的频域预测")
        # 提取高峰期数据
        peak_data = test_inflow[:, -peak_time_steps:, :].transpose(1, 2)  # (batch, station, peak_time_steps)
        fft_output = fft_model(peak_data)
        print(f"   - 高峰期数据: {peak_data.shape}")
        print(f"   - FFT预测输出: {fft_output.shape}")
        print(f"   - 功能: 时域→频域→MLP端到端预测→时域")
        
        # 3. 融合预测演示
        print(f"\n3. 融合预测器:")
        print(f"   - 处理: Transformer和FFT预测结果的智能融合")
        fusion_output = fusion_model(test_inflow)
        print(f"   - 融合输出: {fusion_output.shape}")
        print(f"   - 功能: 基于自适应权重的预测结果融合")
        
        # 4. 预测结果分析
        print(f"\n4. 预测结果分析:")
        transformer_mean = transformer_output.mean().item()
        fft_mean = fft_output.mean().item()
        fusion_mean = fusion_output.mean().item()
        
        print(f"   - Transformer预测均值: {transformer_mean:.6f}")
        print(f"   - FFT预测均值: {fft_mean:.6f}")
        print(f"   - 融合预测均值: {fusion_mean:.6f}")
        
        # 5. 计算融合权重
        input_features = test_inflow.mean(dim=2)  # (batch_size, time_lag*3)
        adaptive_weights = fusion_model.adaptive_fusion(input_features)
        avg_weights = adaptive_weights.mean(dim=0)
        
        print(f"\n5. 自适应融合权重:")
        print(f"   - Transformer权重: {avg_weights[0].item():.3f}")
        print(f"   - FFT权重: {avg_weights[1].item():.3f}")
        print(f"   - 权重说明: 根据输入数据特征动态调整")
    
    return fusion_model, transformer_model, fft_model, test_inflow

def demo_training_step(fusion_model, transformer_model, fft_model, test_inflow):
    """演示训练步骤"""
    print(f"\n" + "=" * 80)
    print("训练步骤演示")
    print("=" * 80)
    
    # 创建优化器和损失函数
    fusion_optimizer = torch.optim.Adam(fusion_model.parameters(), lr=0.001)
    transformer_optimizer = torch.optim.Adam(transformer_model.parameters(), lr=0.001)
    fft_optimizer = torch.optim.Adam(fft_model.parameters(), lr=0.001)
    mse_loss = torch.nn.MSELoss()
    
    # 创建目标数据
    batch_size = test_inflow.size(0)
    target = torch.randn(batch_size, 276, 1).to(test_inflow.device)
    
    print("模拟训练步骤:")
    
    # 1. Transformer训练
    transformer_model.train()
    transformer_pred = transformer_model(test_inflow)
    transformer_loss = mse_loss(transformer_pred, target)
    
    transformer_optimizer.zero_grad()
    transformer_loss.backward()
    transformer_optimizer.step()
    
    print(f"  1. Transformer训练: Loss = {transformer_loss.item():.6f}")
    
    # 2. FFT训练
    fft_model.train()
    peak_data = test_inflow[:, -10:, :].transpose(1, 2)
    fft_pred = fft_model(peak_data)
    fft_loss = mse_loss(fft_pred, target)
    
    fft_optimizer.zero_grad()
    fft_loss.backward()
    fft_optimizer.step()
    
    print(f"  2. FFT训练: Loss = {fft_loss.item():.6f}")
    
    # 3. 融合模型训练
    fusion_model.train()
    fusion_pred = fusion_model(test_inflow)
    fusion_loss = mse_loss(fusion_pred, target)
    
    fusion_optimizer.zero_grad()
    fusion_loss.backward()
    fusion_optimizer.step()
    
    print(f"  3. 融合模型训练: Loss = {fusion_loss.item():.6f}")
    print(f"  4. 训练步骤: 所有模型成功完成一个训练迭代")

def demo_real_data_prediction():
    """使用真实数据进行预测演示"""
    print(f"\n" + "=" * 80)
    print("真实数据预测演示")
    print("=" * 80)
    
    try:
        # 加载真实数据
        print("正在加载真实数据...")
        _, _, test_loader, max_inflow, min_inflow = get_inflow_only_dataloader(
            time_interval=15, time_lag=10, tg_in_one_day=72, 
            forecast_day_number=5, pre_len=1, batch_size=2
        )
        
        # 设备配置
        device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
        
        # 创建模型
        fusion_model = TransformerFFTFusionModel(10, 1, 276, device, 10)
        transformer_model = TransformerPredictor(10, 1, 276, device=device)
        fft_model = FFTPeakPredictor(10, 276, device=device)
        
        fusion_model = fusion_model.to(device)
        transformer_model = transformer_model.to(device)
        fft_model = fft_model.to(device)
        
        # 使用真实数据进行预测
        fusion_model.eval()
        transformer_model.eval()
        fft_model.eval()
        
        with torch.no_grad():
            for i, (real_X, real_Y, real_Y_orig) in enumerate(test_loader):
                if i >= 1:  # 只演示一个批次
                    break
                
                real_X = real_X.type(torch.float32).to(device)
                real_Y = real_Y.type(torch.float32).to(device)
                
                # 调整输入格式
                transformer_X = real_X.transpose(1, 2)  # (batch, time*3, station)
                peak_data = real_X[:, :, -10:]  # (batch, station, 10)
                
                # 各模型预测
                fusion_pred = fusion_model(transformer_X)
                transformer_pred = transformer_model(transformer_X)
                fft_pred = fft_model(peak_data)
                
                # 反归一化
                fusion_orig = fusion_pred.cpu().numpy() * (max_inflow - min_inflow) + min_inflow
                transformer_orig = transformer_pred.cpu().numpy() * (max_inflow - min_inflow) + min_inflow
                fft_orig = fft_pred.cpu().numpy() * (max_inflow - min_inflow) + min_inflow
                true_orig = real_Y_orig.numpy()
                
                print(f"真实数据预测结果 (前5个车站):")
                print(f"{'车站':<6} {'真实值':<8} {'Transformer':<12} {'FFT':<8} {'融合':<8}")
                print("-" * 50)
                
                for station in range(5):
                    true_val = true_orig[0, station, 0]
                    transformer_val = transformer_orig[0, station, 0]
                    fft_val = fft_orig[0, station, 0]
                    fusion_val = fusion_orig[0, station, 0]
                    
                    print(f"{station:<6} {true_val:<8.1f} {transformer_val:<12.1f} {fft_val:<8.1f} {fusion_val:<8.1f}")
                
                # 计算简单指标
                transformer_rmse, transformer_mae, transformer_wmape = calculate_metrics(transformer_orig, true_orig)
                fft_rmse, fft_mae, fft_wmape = calculate_metrics(fft_orig, true_orig)
                fusion_rmse, fusion_mae, fusion_wmape = calculate_metrics(fusion_orig, true_orig)
                
                print(f"\n预测性能 (RMSE):")
                print(f"  - Transformer: {transformer_rmse:.2f}")
                print(f"  - FFT: {fft_rmse:.2f}")
                print(f"  - 融合: {fusion_rmse:.2f}")
                
                break
        
        print("✓ 真实数据预测演示完成")
        
    except Exception as e:
        print(f"真实数据演示失败: {e}")
        print("请确保数据文件存在并且格式正确")

def main():
    """主演示函数"""
    print("欢迎使用 Transformer+FFT 地铁客流预测系统!")
    print("本系统结合了Transformer的完整序列建模和FFT的高峰期频域预测")
    
    try:
        # 架构演示
        fusion_model, transformer_model, fft_model, test_inflow = demo_architecture()
        
        # 训练演示
        demo_training_step(fusion_model, transformer_model, fft_model, test_inflow)
        
        # 真实数据演示
        demo_real_data_prediction()
        
        print(f"\n" + "=" * 80)
        print("演示完成!")
        print("=" * 80)
        print("系统特点:")
        print("  ✅ Transformer: 完整的时间序列预测")
        print("  ✅ FFT: 高峰期频域预测 (10个时间步)")
        print("  ✅ 融合: 自适应权重智能融合")
        print("  ✅ 端到端: 完整的训练和预测流程")
        
        print(f"\n下一步:")
        print("  1. 运行 'python main_transformer_fft.py' 进行完整训练和评估")
        print("  2. 查看生成的预测对比图和评估指标")
        print("  3. 分析Transformer和FFT的融合效果")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        print("请检查:")
        print("  - 数据文件是否存在 (data/in_15min.csv)")
        print("  - 依赖包是否正确安装")
        print("  - 运行 'python test_transformer_fft.py' 进行诊断")

if __name__ == "__main__":
    main()
