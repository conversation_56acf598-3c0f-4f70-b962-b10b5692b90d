import numpy as np
import matplotlib.pyplot as plt
import os

# 设置字体大小（放大3倍）
plt.rcParams.update({
    'font.size': 24,           # 基础字体大小 8*3
    'axes.titlesize': 42,      # 标题字体大小 14*3
    'axes.labelsize': 36,      # 坐标轴标签字体大小 12*3
    'xtick.labelsize': 24,     # x轴刻度标签字体大小 8*3
    'ytick.labelsize': 24,     # y轴刻度标签字体大小 8*3
    'legend.fontsize': 30,     # 图例字体大小 10*3
    'figure.titlesize': 42,    # 图形标题字体大小 14*3
    'xtick.direction': 'in',   # x轴刻度线朝里
    'ytick.direction': 'in',   # y轴刻度线朝里
    'xtick.major.size': 8,     # x轴主刻度线长度
    'ytick.major.size': 8,     # y轴主刻度线长度
    'xtick.minor.size': 4,     # x轴次刻度线长度
    'ytick.minor.size': 4,     # y轴次刻度线长度
    'axes.linewidth': 2,       # 坐标轴线宽
})

def generate_data():
    """生成模拟数据"""
    np.random.seed(33)
    time_steps = 50
    
    # 生成基础真实值
    base_flow = 200
    peak_pattern = np.sin(np.linspace(0, 4*np.pi, time_steps)) * 100 + base_flow
    noise = np.random.normal(0, 15, time_steps)
    true_values = peak_pattern + noise
    true_values = np.maximum(true_values, 0)
    
    # 生成预测值
    main_pred = true_values + np.random.normal(0, 20, time_steps)
    main_pred = np.maximum(main_pred, 0)
    
    fft_pred = true_values.copy()
    fft_pred[30:36] += np.random.normal(0, 8, 6)
    other_indices = list(range(0, 30)) + list(range(36, time_steps))
    fft_pred[other_indices] += np.random.normal(0, 25, len(other_indices))
    fft_pred = np.maximum(fft_pred, 0)
    
    hybrid_pred = (main_pred + fft_pred) / 2
    
    return true_values, main_pred, fft_pred, hybrid_pred

def calculate_rmse(y_true, y_pred):
    """计算RMSE"""
    return np.sqrt(np.mean((y_true - y_pred) ** 2))

def plot_main_comparison():
    """生成主对比图"""
    print("生成station_A_main_comparison.png...")
    
    true_values, main_pred, fft_pred, hybrid_pred = generate_data()
    x_axis = np.arange(50)
    
    # 计算RMSE
    main_rmse = calculate_rmse(true_values, main_pred)
    fft_rmse = calculate_rmse(true_values, fft_pred)
    hybrid_rmse = calculate_rmse(true_values, hybrid_pred)
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    
    # 绘制数据
    ax.plot(x_axis, true_values, 'k-', linewidth=4, marker='o', markersize=8, label='True Values')
    ax.plot(x_axis, main_pred, 'b--', linewidth=3.5, marker='s', markersize=7, 
            label=f'GCN+Transformer (RMSE: {main_rmse:.2f})')
    ax.plot(x_axis, fft_pred, 'r:', linewidth=3.5, marker='^', markersize=7, 
            label=f'FFT (RMSE: {fft_rmse:.2f})')
    ax.plot(x_axis, hybrid_pred, 'g-.', linewidth=3.5, marker='d', markersize=7, 
            label=f'FFT+GCN+Trans (RMSE: {hybrid_rmse:.2f})')
    
    # 标记放大区域
    ax.axvspan(30, 35, alpha=0.2, color='red')
    y_max = max(np.max(true_values[30:36]), np.max(main_pred[30:36]),
               np.max(fft_pred[30:36]), np.max(hybrid_pred[30:36]))
    ax.text(32.5, y_max + 10, 'Zoom 1', ha='center', va='bottom',
            fontsize=24, color='red', fontweight='bold')
    
    # 设置标题和标签
    ax.set_title('Station A - Peak Time Prediction Comparison\n5 Days × 10 Time Steps per Day', 
                fontweight='bold', pad=20)
    ax.set_xlabel('Time Steps', labelpad=15)
    ax.set_ylabel('Passenger Flow', labelpad=15)
    ax.legend(loc='best', frameon=True, fancybox=True, shadow=True)
    ax.grid(True, alpha=0.3, linewidth=1)
    ax.set_xlim(0, 49)
    
    plt.tight_layout()
    
    # 确保目录存在
    os.makedirs('result', exist_ok=True)
    plt.savefig('result/station_A_main_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✓ station_A_main_comparison.png 已生成")

def plot_zoom_comparison():
    """生成局部放大图"""
    print("生成station_A_zoom_1_30_35.png...")
    
    true_values, main_pred, fft_pred, hybrid_pred = generate_data()
    
    # 局部区域 30-35
    start, end = 30, 35
    x_zoom = np.arange(start, end + 1)
    
    true_zoom = true_values[start:end+1]
    main_zoom = main_pred[start:end+1]
    fft_zoom = fft_pred[start:end+1]
    hybrid_zoom = hybrid_pred[start:end+1]
    
    # 计算局部RMSE
    main_rmse = calculate_rmse(true_zoom, main_zoom)
    fft_rmse = calculate_rmse(true_zoom, fft_zoom)
    hybrid_rmse = calculate_rmse(true_zoom, hybrid_zoom)
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(14, 10))
    
    # 绘制数据
    ax.plot(x_zoom, true_zoom, 'k-', linewidth=4, marker='o', markersize=10, label='True Values')
    ax.plot(x_zoom, main_zoom, 'b--', linewidth=3.5, marker='s', markersize=8, 
            label=f'GCN+Transformer (RMSE: {main_rmse:.2f})')
    ax.plot(x_zoom, fft_zoom, 'r:', linewidth=3.5, marker='^', markersize=8, 
            label=f'FFT (RMSE: {fft_rmse:.2f})')
    ax.plot(x_zoom, hybrid_zoom, 'g-.', linewidth=3.5, marker='d', markersize=8, 
            label=f'FFT+GCN+Trans (RMSE: {hybrid_rmse:.2f})')
    
    # 设置范围
    ax.set_xlim(start, end)
    y_min = min(np.min(true_zoom), np.min(main_zoom), np.min(fft_zoom), np.min(hybrid_zoom))
    y_max = max(np.max(true_zoom), np.max(main_zoom), np.max(fft_zoom), np.max(hybrid_zoom))
    y_range = y_max - y_min
    ax.set_ylim(y_min - y_range*0.1, y_max + y_range*0.1)
    
    # 设置标题和标签
    ax.set_title('Station A - Zoom 1 (Time Steps 30-35)\nDetailed Comparison', 
                fontweight='bold', pad=20)
    ax.set_xlabel('Time Steps', labelpad=15)
    ax.set_ylabel('Passenger Flow', labelpad=15)
    ax.legend(loc='best', frameon=True, fancybox=True, shadow=True)
    ax.grid(True, alpha=0.3, linewidth=1)
    
    plt.tight_layout()
    plt.savefig('result/station_A_zoom_1_30_35.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✓ station_A_zoom_1_30_35.png 已生成")

# 主程序
print("=" * 80)
print("重新生成Station A图片")
print("设置: 刻度线朝里，字体放大3倍")
print("=" * 80)

try:
    plot_main_comparison()
    plot_zoom_comparison()
    
    print("\n" + "=" * 80)
    print("图片生成完成！")
    print("生成文件:")
    print("- result/station_A_main_comparison.png (车站A主对比图)")
    print("- result/station_A_zoom_1_30_35.png (车站A局部放大图 30-35)")
    print("\n特点:")
    print("✓ 刻度线朝里")
    print("✓ 字体放大3倍")
    print("✓ 高分辨率 (300 DPI)")
    print("✓ 专业图表样式")
    print("=" * 80)
    
except Exception as e:
    print(f"生成图片时出错: {e}")
    import traceback
    traceback.print_exc()
