import numpy as np
import os, time, torch
from torch import nn
from torch.utils.tensorboard import SummaryWriter
from utils.utils import GetLaplacian
from model.main_model import Model
import matplotlib.pyplot as plt
from utils.metrics import Metrics, Metrics_1d
from data.get_dataloader import get_inflow_dataloader, get_outflow_dataloader

device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# 参数设置
epoch_num = 1000
lr = 0.0005
time_interval = 15
time_lag = 10
tg_in_one_day = 72
forecast_day_number = 5
pre_len = 1
batch_size = 32
station_num = 276
model_type = 'improved'

# 加载数据
inflow_data_loader_train, inflow_data_loader_val, inflow_data_loader_test, max_inflow, min_inflow = \
	get_inflow_dataloader(time_interval=time_interval, time_lag=time_lag, tg_in_one_day=tg_in_one_day, 
						  forecast_day_number=forecast_day_number, pre_len=pre_len, batch_size=batch_size)
outflow_data_loader_train, outflow_data_loader_val, outflow_data_loader_test, max_outflow, min_outflow = \
	get_outflow_dataloader(time_interval=time_interval, time_lag=time_lag, tg_in_one_day=tg_in_one_day, 
						   forecast_day_number=forecast_day_number, pre_len=pre_len, batch_size=batch_size)

print(f"Data normalization - max_inflow: {max_inflow}, min_inflow: {min_inflow}")
print(f"Data normalization - max_outflow: {max_outflow}, min_outflow: {min_outflow}")

# 获取归一化的邻接矩阵
adjacency = np.loadtxt('./data/adjacency.csv', delimiter=",")
adjacency = torch.tensor(GetLaplacian(adjacency).get_normalized_adj(station_num)).type(torch.float32).to(device)

# 创建模型
model = Model(time_lag, pre_len, station_num, device)
if torch.cuda.is_available():
	model.cuda()
model = model.to(device)

# 加载训练好的模型 - 请根据实际情况修改路径
model_path = 'D:/42972源代码/深度学习与交通大数据实战---源代码与数据集5/第4章_地铁代码 - transform优化了的版本 - 2/subway flow prediction/save_model/improved_2025_05_29_14_59_17/model_dict_checkpoint_298_0.00010852.pth'

try:
	checkpoint = torch.load(model_path, map_location=device)
	model.load_state_dict(checkpoint, strict=False)  # 使用strict=False以适应模型结构变化
	print(f"Model loaded successfully from {model_path}")
except Exception as e:
	print(f"Error loading model: {e}")
	print("Training a new model instead...")
	# 如果加载失败，可以选择训练新模型或使用随机初始化的模型进行测试

optimizer = torch.optim.Adam(model.parameters(), lr=lr)
mse = torch.nn.MSELoss().to(device)

# 测试阶段
print("Starting prediction...")
result = []
result_original = []
predictions_raw = []  # 保存原始预测值（归一化后的）

if not os.path.exists('result/prediction'):
	os.makedirs('result/prediction/')
if not os.path.exists('result/original'):
	os.makedirs('result/original')

with torch.no_grad():
	model.eval()
	test_loss = 0
	batch_count = 0
	
	for inflow_te, outflow_te in zip(enumerate(inflow_data_loader_test), enumerate(outflow_data_loader_test)):
		i_batch, (test_inflow_X, test_inflow_Y, test_inflow_Y_original) = inflow_te
		i_batch, (test_outflow_X, test_outflow_Y, test_outflow_Y_original) = outflow_te
		
		test_inflow_X = test_inflow_X.type(torch.float32).to(device)
		test_inflow_Y = test_inflow_Y.type(torch.float32).to(device)
		test_outflow_X = test_outflow_X.type(torch.float32).to(device)
		test_outflow_Y = test_outflow_Y.type(torch.float32).to(device)

		# 模型预测
		target = model(test_inflow_X, test_outflow_X, adjacency)
		
		# 计算损失
		loss = mse(target, test_inflow_Y)
		test_loss += loss.item()

		# 保存原始预测值（归一化后的）
		target_np = target.cpu().detach().numpy().copy()
		predictions_raw.extend(target_np)
		
		# 反归一化预测结果
		clone_prediction = target_np * (max_inflow - min_inflow) + min_inflow
		for i in range(clone_prediction.shape[0]):
			result.append(clone_prediction[i])

		# 获取真实值
		test_inflow_Y_original = test_inflow_Y_original.cpu().detach().numpy()
		for i in range(test_inflow_Y_original.shape[0]):
			result_original.append(test_inflow_Y_original[i])
		
		batch_count += 1
		if batch_count % 10 == 0:
			print(f"Processed {batch_count} batches...")

	print(f"Prediction completed. Total batches: {batch_count}")
	print(f"Result shape: {np.array(result).shape}, Original shape: {np.array(result_original).shape}")
	
	# 数据后处理
	result = np.array(result).astype(int)
	result[result < 0] = 0  # 确保非负
	result_original = np.array(result_original).astype(int)
	result_original[result_original < 0] = 0
	
	# 检查预测值的统计信息
	predictions_raw = np.array(predictions_raw)
	print(f"Raw predictions stats - Min: {predictions_raw.min():.6f}, Max: {predictions_raw.max():.6f}, Mean: {predictions_raw.mean():.6f}, Std: {predictions_raw.std():.6f}")
	print(f"Final predictions stats - Min: {result.min()}, Max: {result.max()}, Mean: {result.mean():.2f}, Std: {result.std():.2f}")
	print(f"Original data stats - Min: {result_original.min()}, Max: {result_original.max()}, Mean: {result_original.mean():.2f}, Std: {result_original.std():.2f}")
	
	# 重塑数据用于整体评估和保存
	result_reshaped = np.array(result).reshape(-1, station_num)  # (时间步, 车站数)
	result_original_reshaped = result_original.reshape(-1, station_num)  # (时间步, 车站数)

	print(f"Reshaped data - Predictions: {result_reshaped.shape}, Original: {result_original_reshaped.shape}")

	# 转置以便按车站保存 (车站数, 时间步)
	result_by_station = result_reshaped.T  # (276, 时间步)
	original_by_station = result_original_reshaped.T  # (276, 时间步)

	# 选择多个车站进行可视化
	station_indices = [4, 18, 30, 60, 94]
	x = [[] for _ in range(len(station_indices))]
	y = [[] for _ in range(len(station_indices))]

	for i in range(result.shape[0]):
		for j, station_idx in enumerate(station_indices):
			x[j].append(result[i][station_idx][0])
			y[j].append(result_original[i][station_idx][0])

	# 计算整体性能指标
	RMSE, R2, MAE, WMAPE = Metrics(original_by_station, result_by_station).evaluate_performance()

	avg_test_loss = test_loss / len(inflow_data_loader_test)
	print(f'Test Loss: {avg_test_loss:.6f}')

	# 计算各个车站的性能指标
	station_metrics = []
	for j, station_idx in enumerate(station_indices):
		RMSE_station, R2_station, MAE_station, WMAPE_station = Metrics_1d(y[j], x[j]).evaluate_performance()
		station_metrics.append([RMSE_station, MAE_station, WMAPE_station])
		print(f'Station {station_idx} - RMSE: {RMSE_station:.2f}, MAE: {MAE_station:.2f}, WMAPE: {WMAPE_station:.4f}')

	# 计算所有276个车站的详细性能指标
	print("\n正在计算所有276个车站的详细性能指标...")
	all_station_metrics = []
	for station_idx in range(station_num):
		station_pred = result_by_station[station_idx]
		station_orig = original_by_station[station_idx]

		if len(station_pred) > 0 and len(station_orig) > 0:
			RMSE_s, R2_s, MAE_s, WMAPE_s = Metrics_1d(station_orig, station_pred).evaluate_performance()
			all_station_metrics.append([station_idx, RMSE_s, R2_s, MAE_s, WMAPE_s])
		else:
			all_station_metrics.append([station_idx, 0, 0, 0, 0])

	all_station_metrics = np.array(all_station_metrics)
	print(f"所有车站性能指标计算完成，形状: {all_station_metrics.shape}")

# 保存结果
ALL = [RMSE, MAE, WMAPE]
print(f"Overall Performance - RMSE: {RMSE:.2f}, MAE: {MAE:.2f}, WMAPE: {WMAPE:.4f}")

# 保存结果到文件
if not os.path.exists('result'):
	os.makedirs('result')

# 保存整体性能指标
np.savetxt(f'result/improved_lr_{lr}_batch_size_{batch_size}_ALL.txt', ALL)

# 保存选定车站的性能指标
for j, station_idx in enumerate(station_indices):
	np.savetxt(f'result/improved_lr_{lr}_batch_size_{batch_size}_station_{station_idx}_ALL.txt', station_metrics[j])

# 保存选定车站的预测结果（用于可视化）
np.savetxt('result/improved_X_prediction.txt', x)
np.savetxt('result/improved_Y_original.txt', y)

# 保存全部276个车站的预测结果和原始值
print("正在保存全部276个车站的预测结果...")
np.savetxt('result/improved_ALL_276_stations_predictions.txt', result_by_station, fmt='%.6f')
np.savetxt('result/improved_ALL_276_stations_original.txt', original_by_station, fmt='%.6f')

# 保存所有车站的性能指标
np.savetxt('result/improved_ALL_276_stations_metrics.txt', all_station_metrics,
		   fmt='%d %.6f %.6f %.6f %.6f',
		   header='StationID RMSE R2 MAE WMAPE')

print(f"全部276个车站数据已保存:")
print(f"- 预测结果: result/improved_ALL_276_stations_predictions.txt (形状: {result_by_station.shape})")
print(f"- 原始值: result/improved_ALL_276_stations_original.txt (形状: {original_by_station.shape})")
print(f"- 性能指标: result/improved_ALL_276_stations_metrics.txt (形状: {all_station_metrics.shape})")

# 设置中文字体以避免乱码
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 可视化结果 - 显示测试集全部时间步数据
plt.figure(figsize=(20, 12))

# 获取测试集的实际长度
test_length = len(x[0])
print(f"Test set length: {test_length} time steps")

# 绘制多个车站的预测结果 - 显示全部测试数据
for j, station_idx in enumerate(station_indices):
	plt.subplot(2, 3, j+1)
	plt.plot(x[j], color="red", label="Prediction", linewidth=1.5, alpha=0.8)
	plt.plot(y[j], color="blue", label="Actual", linewidth=1.5, alpha=0.8)
	# plt.title(f'Station {station_idx} - All {test_length} Time Steps', fontsize=12)
	plt.title(f'Station {station_idx} ', fontsize=12)
	plt.xlabel('Time Steps', fontsize=10)
	plt.ylabel('Passenger Flow', fontsize=10)
	plt.legend(fontsize=10)
	plt.grid(True, alpha=0.3)

# 绘制整体对比图 - 显示全部测试数据
plt.subplot(2, 3, 6)
plt.plot(x[0], color="red", label="Prediction", linewidth=2, alpha=0.8)
plt.plot(y[0], color="blue", label="Actual", linewidth=2, alpha=0.8)
plt.title(f'Station 4 Detailed View - All {test_length} Time Steps', fontsize=12)
plt.xlabel('Time Steps (Test Set)', fontsize=10)
plt.ylabel('Passenger Flow', fontsize=10)
plt.legend(fontsize=10)
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('result/improved_prediction_comparison.png', dpi=300, bbox_inches='tight')
plt.show()

print("Prediction analysis completed!")
print(f"Results saved to 'result/' directory")
