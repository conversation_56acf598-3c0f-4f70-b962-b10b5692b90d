#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分析main_predict_improved.py模型在FFT选定时刻点的预测性能
使用已有的预测结果文件进行分析

Author: 008 - AI Algorithm Engineer for Subway Flow Prediction
"""

import numpy as np
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import os

def load_main_predict_results():
    """加载main_predict的预测结果"""
    print("正在加载main_predict的预测结果...")
    
    # 加载预测值
    predictions = np.loadtxt('result/improved_X_prediction.txt')
    print(f"预测值形状: {predictions.shape}")
    
    # 加载真实值
    true_values = np.loadtxt('result/improved_Y_original.txt')
    print(f"真实值形状: {true_values.shape}")
    
    return predictions, true_values

def load_fft_timepoint_mapping():
    """加载FFT选定的时刻点映射"""
    print("正在加载FFT时刻点映射...")
    
    # 加载原始数据
    raw_data = np.loadtxt('./data/in_15min.csv', delimiter=",")
    if raw_data.shape[0] != 276:
        raw_data = raw_data.T
    
    # 加载高峰时刻信息
    peak_times = np.loadtxt('./data/continuous_peak_flow_dataset_peak_times.csv', delimiter=',', dtype=int)
    
    # 构建FFT选定的时刻点映射
    station_num = 276
    time_steps_per_day = 72
    total_days = 25
    sequence_length = 10
    
    fft_timepoint_indices = []  # 存储每个车站FFT选定的时刻点在原始数据中的索引
    
    for station_idx in range(station_num):
        station_indices = []
        peak_time = peak_times[station_idx]
        
        for day in range(total_days):
            day_start = day * time_steps_per_day
            
            # 计算当天对应的序列位置
            start_idx = max(0, day_start + peak_time - 4)
            end_idx = min(raw_data.shape[1], day_start + peak_time + 6)
            
            # 获取序列索引
            if end_idx - start_idx >= sequence_length:
                indices = list(range(start_idx, start_idx + sequence_length))
            else:
                # 处理边界情况
                indices = list(range(start_idx, end_idx))
                if len(indices) < sequence_length:
                    # 填充
                    pad_length = sequence_length - len(indices)
                    if start_idx == 0:
                        indices.extend([indices[-1]] * pad_length)
                    else:
                        indices = [indices[0]] * pad_length + indices
            
            station_indices.extend(indices)
        
        fft_timepoint_indices.append(station_indices)
    
    return fft_timepoint_indices, peak_times

def extract_predictions_at_fft_timepoints(predictions, true_values, fft_indices):
    """提取FFT时刻点的预测值和真实值"""
    print("正在提取FFT时刻点的预测值和真实值...")

    # 检查实际可用的车站数量
    num_available_stations = min(len(predictions), len(true_values))
    print(f"可用车站数量: {num_available_stations}")

    fft_predictions = []
    fft_true_values = []

    for station_idx in range(num_available_stations):
        station_fft_indices = fft_indices[station_idx]  # 该车站的250个时刻点索引

        # 提取对应的预测值和真实值
        station_predictions = []
        station_true_values = []

        for time_idx in station_fft_indices:
            # 确保索引在有效范围内
            if time_idx < len(predictions[station_idx]) and time_idx < len(true_values[station_idx]):
                station_predictions.append(predictions[station_idx][time_idx])
                station_true_values.append(true_values[station_idx][time_idx])
            else:
                # 如果索引超出范围，使用最后一个有效值
                station_predictions.append(predictions[station_idx][-1])
                station_true_values.append(true_values[station_idx][-1])

        fft_predictions.extend(station_predictions)
        fft_true_values.extend(station_true_values)

    return np.array(fft_predictions), np.array(fft_true_values), num_available_stations

def calculate_metrics(predictions, true_values):
    """计算评估指标"""
    print("正在计算评估指标...")
    
    # 计算RMSE
    rmse = np.sqrt(mean_squared_error(true_values, predictions))
    
    # 计算MAE
    mae = mean_absolute_error(true_values, predictions)
    
    # 计算WMAPE
    mask = true_values > 0
    if np.sum(mask) > 0:
        wmape = np.sum(np.abs(predictions[mask] - true_values[mask])) / np.sum(true_values[mask])
    else:
        wmape = 0
    
    # 计算R2
    r2 = r2_score(true_values, predictions)
    
    return rmse, mae, wmape, r2

def calculate_station_level_metrics(predictions, true_values, fft_indices, peak_times, num_available_stations):
    """计算各车站级别的评估指标"""
    print("正在计算各车站级别的评估指标...")

    station_metrics = []

    for station_idx in range(num_available_stations):
        station_fft_indices = fft_indices[station_idx]

        # 提取该车站的预测值和真实值
        station_predictions = []
        station_true_values = []

        for time_idx in station_fft_indices:
            if time_idx < len(predictions[station_idx]) and time_idx < len(true_values[station_idx]):
                station_predictions.append(predictions[station_idx][time_idx])
                station_true_values.append(true_values[station_idx][time_idx])
            else:
                station_predictions.append(predictions[station_idx][-1])
                station_true_values.append(true_values[station_idx][-1])

        station_predictions = np.array(station_predictions)
        station_true_values = np.array(station_true_values)

        # 计算该车站的指标
        station_rmse = np.sqrt(mean_squared_error(station_true_values, station_predictions))
        station_mae = mean_absolute_error(station_true_values, station_predictions)

        mask = station_true_values > 0
        if np.sum(mask) > 0:
            station_wmape = np.sum(np.abs(station_predictions[mask] - station_true_values[mask])) / np.sum(station_true_values[mask])
        else:
            station_wmape = 0

        station_r2 = r2_score(station_true_values, station_predictions)

        station_metrics.append({
            'station_id': station_idx,
            'peak_time': peak_times[station_idx],
            'rmse': station_rmse,
            'mae': station_mae,
            'wmape': station_wmape,
            'r2': station_r2
        })

    return station_metrics

def save_results(overall_rmse, overall_mae, overall_wmape, overall_r2,
                station_metrics, fft_predictions, fft_true_values, num_available_stations):
    """保存分析结果"""
    if not os.path.exists('result'):
        os.makedirs('result')

    # 保存总体结果
    with open('result/main_predict_fft_timepoints_analysis.txt', 'w', encoding='utf-8') as f:
        f.write("main_predict_improved.py模型在FFT选定时刻点的预测性能分析\n")
        f.write("=" * 80 + "\n\n")

        f.write("分析说明:\n")
        f.write("-" * 40 + "\n")
        f.write("- 使用main_predict_improved.py的预测结果文件\n")
        f.write("- 在fft_continuous_prediction.py选定的250个时刻点上进行评估\n")
        f.write("- 每个车站25天×10时间步 = 250个时刻点\n")
        f.write(f"- 实际可用车站数: {num_available_stations}个\n")
        f.write(f"- 总计{num_available_stations}个车站 × 250个时刻点 = {num_available_stations * 250}个预测点\n\n")
        
        f.write("整体评估指标:\n")
        f.write("-" * 40 + "\n")
        f.write(f"RMSE: {overall_rmse:.4f}\n")
        f.write(f"MAE: {overall_mae:.4f}\n")
        f.write(f"WMAPE: {overall_wmape:.4f} ({overall_wmape*100:.2f}%)\n")
        f.write(f"R2: {overall_r2:.4f}\n\n")
        
        f.write("数据统计:\n")
        f.write("-" * 40 + "\n")
        f.write(f"预测值范围: [{np.min(fft_predictions):.2f}, {np.max(fft_predictions):.2f}]\n")
        f.write(f"真实值范围: [{np.min(fft_true_values):.2f}, {np.max(fft_true_values):.2f}]\n")
        f.write(f"预测值平均: {np.mean(fft_predictions):.2f}\n")
        f.write(f"真实值平均: {np.mean(fft_true_values):.2f}\n\n")
        
        # 各车站指标统计
        rmses = [m['rmse'] for m in station_metrics]
        maes = [m['mae'] for m in station_metrics]
        wmapes = [m['wmape'] for m in station_metrics]
        r2s = [m['r2'] for m in station_metrics]
        
        f.write("各车站指标统计:\n")
        f.write("-" * 40 + "\n")
        f.write(f"RMSE - 平均: {np.mean(rmses):.2f}, 标准差: {np.std(rmses):.2f}, 最小: {np.min(rmses):.2f}, 最大: {np.max(rmses):.2f}\n")
        f.write(f"MAE  - 平均: {np.mean(maes):.2f}, 标准差: {np.std(maes):.2f}, 最小: {np.min(maes):.2f}, 最大: {np.max(maes):.2f}\n")
        f.write(f"WMAPE- 平均: {np.mean(wmapes):.4f}, 标准差: {np.std(wmapes):.4f}, 最小: {np.min(wmapes):.4f}, 最大: {np.max(wmapes):.4f}\n")
        f.write(f"R2   - 平均: {np.mean(r2s):.4f}, 标准差: {np.std(r2s):.4f}, 最小: {np.min(r2s):.4f}, 最大: {np.max(r2s):.4f}\n\n")
        
        # 表现最好和最差的车站
        best_rmse_idx = np.argmin(rmses)
        worst_rmse_idx = np.argmax(rmses)
        best_r2_idx = np.argmax(r2s)
        worst_r2_idx = np.argmin(r2s)
        
        f.write("表现最好的车站:\n")
        f.write("-" * 40 + "\n")
        f.write(f"RMSE最小 - 车站{best_rmse_idx}: RMSE={rmses[best_rmse_idx]:.2f}, MAE={maes[best_rmse_idx]:.2f}, WMAPE={wmapes[best_rmse_idx]:.4f}, R2={r2s[best_rmse_idx]:.4f}\n")
        f.write(f"R2最大   - 车站{best_r2_idx}: RMSE={rmses[best_r2_idx]:.2f}, MAE={maes[best_r2_idx]:.2f}, WMAPE={wmapes[best_r2_idx]:.4f}, R2={r2s[best_r2_idx]:.4f}\n\n")
        
        f.write("表现最差的车站:\n")
        f.write("-" * 40 + "\n")
        f.write(f"RMSE最大 - 车站{worst_rmse_idx}: RMSE={rmses[worst_rmse_idx]:.2f}, MAE={maes[worst_rmse_idx]:.2f}, WMAPE={wmapes[worst_rmse_idx]:.4f}, R2={r2s[worst_rmse_idx]:.4f}\n")
        f.write(f"R2最小   - 车站{worst_r2_idx}: RMSE={rmses[worst_r2_idx]:.2f}, MAE={maes[worst_r2_idx]:.2f}, WMAPE={wmapes[worst_r2_idx]:.4f}, R2={r2s[worst_r2_idx]:.4f}\n\n")
        
        # 前20个车站的详细指标
        f.write("前20个车站详细指标:\n")
        f.write("-" * 80 + "\n")
        f.write("车站ID | 高峰时刻 | RMSE     | MAE      | WMAPE    | R2       \n")
        f.write("-" * 80 + "\n")
        
        for i in range(min(20, len(station_metrics))):
            metrics = station_metrics[i]
            peak_time = metrics['peak_time']
            hour = peak_time // 4
            minute = (peak_time % 4) * 15
            
            f.write(f"{metrics['station_id']:6d} | {hour:02d}:{minute:02d}     | {metrics['rmse']:8.2f} | {metrics['mae']:8.2f} | {metrics['wmape']:8.4f} | {metrics['r2']:8.4f}\n")
        
        f.write("...\n")
        f.write(f"(显示前20个车站，共276个车站的详细数据)\n")
    
    print("分析结果已保存到 result/main_predict_fft_timepoints_analysis.txt")

def main():
    """主函数"""
    print("=" * 80)
    print("main_predict_improved.py模型在FFT选定时刻点的预测性能分析")
    print("=" * 80)
    
    # 1. 加载main_predict的预测结果
    predictions, true_values = load_main_predict_results()
    
    # 2. 加载FFT时刻点映射
    fft_indices, peak_times = load_fft_timepoint_mapping()
    
    # 3. 提取FFT时刻点的预测值和真实值
    fft_predictions, fft_true_values, num_available_stations = extract_predictions_at_fft_timepoints(
        predictions, true_values, fft_indices)

    # 4. 计算整体评估指标
    overall_rmse, overall_mae, overall_wmape, overall_r2 = calculate_metrics(
        fft_predictions, fft_true_values)

    # 5. 计算各车站级别的指标
    station_metrics = calculate_station_level_metrics(
        predictions, true_values, fft_indices, peak_times, num_available_stations)

    # 6. 输出结果
    print("\n" + "=" * 80)
    print("main_predict模型在FFT选定时刻点的预测性能:")
    print("-" * 80)
    print(f"可用车站数: {num_available_stations}")
    print(f"总预测点数: {len(fft_predictions)} ({num_available_stations}个车站 × 250个时刻点)")
    print(f"RMSE: {overall_rmse:.4f}")
    print(f"MAE: {overall_mae:.4f}")
    print(f"WMAPE: {overall_wmape:.4f} ({overall_wmape*100:.2f}%)")
    print(f"R2: {overall_r2:.4f}")
    print("=" * 80)

    # 7. 保存详细结果
    save_results(overall_rmse, overall_mae, overall_wmape, overall_r2,
                station_metrics, fft_predictions, fft_true_values, num_available_stations)
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()
