"""
测试Transformer+FFT模型的完整性和正确性
"""
import torch
import numpy as np
import sys
import os

# 添加当前目录到Python路径
if '.' not in sys.path:
    sys.path.append('.')

def test_transformer_predictor():
    """测试Transformer预测器"""
    print("Testing Transformer Predictor...")
    try:
        from model.transformer_fft_model import TransformerPredictor
        
        device = torch.device('cpu')
        time_lag, pre_len, station_num = 10, 1, 276
        predictor = TransformerPredictor(time_lag, pre_len, station_num, device=device)
        
        # 测试数据 (batch_size, time_lag*3, station_num)
        batch_size = 2
        test_data = torch.randn(batch_size, time_lag * 3, station_num)
        
        # 前向传播
        with torch.no_grad():
            output = predictor(test_data)
        
        print(f"  ✓ Transformer Predictor: {test_data.shape} -> {output.shape}")
        
        # 检查输出维度
        expected_shape = (batch_size, station_num, pre_len)
        if output.shape == expected_shape:
            print(f"  ✓ Output shape correct: {output.shape}")
            return True
        else:
            print(f"  ✗ Output shape incorrect: expected {expected_shape}, got {output.shape}")
            return False
        
    except Exception as e:
        print(f"  ✗ Transformer Predictor test failed: {e}")
        return False

def test_fft_peak_predictor():
    """测试FFT高峰期预测器"""
    print("Testing FFT Peak Predictor...")
    try:
        from model.transformer_fft_model import FFTPeakPredictor
        
        device = torch.device('cpu')
        peak_time_steps, station_num = 10, 276
        predictor = FFTPeakPredictor(peak_time_steps, station_num, device=device)
        
        # 测试数据 (batch_size, station_num, peak_time_steps)
        batch_size = 2
        test_data = torch.randn(batch_size, station_num, peak_time_steps)
        
        # 前向传播
        with torch.no_grad():
            output = predictor(test_data)
        
        print(f"  ✓ FFT Peak Predictor: {test_data.shape} -> {output.shape}")
        
        # 检查输出维度
        expected_shape = (batch_size, station_num, 1)
        if output.shape == expected_shape:
            print(f"  ✓ Output shape correct: {output.shape}")
            return True
        else:
            print(f"  ✗ Output shape incorrect: expected {expected_shape}, got {output.shape}")
            return False
        
    except Exception as e:
        print(f"  ✗ FFT Peak Predictor test failed: {e}")
        return False

def test_fusion_model():
    """测试融合模型"""
    print("Testing Transformer+FFT Fusion Model...")
    try:
        from model.transformer_fft_model import TransformerFFTFusionModel
        
        device = torch.device('cpu')
        time_lag, pre_len, station_num, peak_time_steps = 10, 1, 276, 10
        model = TransformerFFTFusionModel(time_lag, pre_len, station_num, device, peak_time_steps)
        
        # 测试数据 (batch_size, time_lag*3, station_num)
        batch_size = 2
        test_data = torch.randn(batch_size, time_lag * 3, station_num)
        
        # 前向传播
        with torch.no_grad():
            output = model(test_data)
        
        print(f"  ✓ Fusion Model: {test_data.shape} -> {output.shape}")
        
        # 检查输出维度
        expected_shape = (batch_size, station_num, pre_len)
        if output.shape == expected_shape:
            print(f"  ✓ Output shape correct: {output.shape}")
            
            # 测试模型信息
            model_info = model.get_model_info()
            print(f"  ✓ Model parameters: {model_info['total_parameters']:,}")
            return True
        else:
            print(f"  ✗ Output shape incorrect: expected {expected_shape}, got {output.shape}")
            return False
        
    except Exception as e:
        print(f"  ✗ Fusion Model test failed: {e}")
        return False

def test_data_compatibility():
    """测试数据兼容性"""
    print("Testing Data Compatibility...")
    try:
        # 检查数据文件
        if not os.path.exists('./data/in_15min.csv'):
            print("  ✗ Data file './data/in_15min.csv' not found")
            return False
        
        from data.get_inflow_only_dataloader import get_inflow_only_dataloader
        
        # 测试数据加载
        train_loader, val_loader, test_loader, max_val, min_val = get_inflow_only_dataloader(
            time_interval=15, time_lag=10, tg_in_one_day=72, 
            forecast_day_number=5, pre_len=1, batch_size=4
        )
        
        # 测试一个批次
        for X, Y in train_loader:
            print(f"  ✓ Data compatibility: X.shape={X.shape}, Y.shape={Y.shape}")
            break
        
        return True
    except Exception as e:
        print(f"  ✗ Data compatibility test failed: {e}")
        return False

def test_training_compatibility():
    """测试训练兼容性"""
    print("Testing Training Compatibility...")
    try:
        from model.transformer_fft_model import TransformerPredictor, FFTPeakPredictor
        
        device = torch.device('cpu')
        time_lag, pre_len, station_num, peak_time_steps = 10, 1, 276, 10
        
        # 测试Transformer训练
        transformer_model = TransformerPredictor(time_lag, pre_len, station_num, device=device)
        transformer_optimizer = torch.optim.Adam(transformer_model.parameters(), lr=0.001)
        mse_loss = torch.nn.MSELoss()
        
        # 模拟训练数据
        batch_size = 2
        train_X = torch.randn(batch_size, time_lag * 3, station_num)
        train_Y = torch.randn(batch_size, station_num, pre_len)
        
        # Transformer训练步骤
        transformer_model.train()
        transformer_pred = transformer_model(train_X)
        transformer_loss = mse_loss(transformer_pred, train_Y)
        
        transformer_optimizer.zero_grad()
        transformer_loss.backward()
        transformer_optimizer.step()
        
        print(f"  ✓ Transformer training step completed, loss: {transformer_loss.item():.6f}")
        
        # 测试FFT训练
        fft_model = FFTPeakPredictor(peak_time_steps, station_num, device=device)
        fft_optimizer = torch.optim.Adam(fft_model.parameters(), lr=0.001)
        
        # 模拟FFT训练数据
        peak_data = torch.randn(batch_size, station_num, peak_time_steps)
        
        # FFT训练步骤
        fft_model.train()
        fft_pred = fft_model(peak_data)
        fft_loss = mse_loss(fft_pred, train_Y)
        
        fft_optimizer.zero_grad()
        fft_loss.backward()
        fft_optimizer.step()
        
        print(f"  ✓ FFT training step completed, loss: {fft_loss.item():.6f}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Training compatibility test failed: {e}")
        return False

def test_metrics_calculation():
    """测试指标计算"""
    print("Testing Metrics Calculation...")
    try:
        from main_transformer_fft import calculate_metrics
        
        # 创建测试数据
        predictions = np.random.randn(100, 276, 1)
        true_values = np.random.randn(100, 276, 1)
        
        # 计算指标
        rmse, mae, wmape = calculate_metrics(predictions, true_values)
        
        print(f"  ✓ Metrics calculated: RMSE={rmse:.4f}, MAE={mae:.4f}, WMAPE={wmape:.4f}%")
        
        # 验证指标合理性
        if rmse > 0 and mae > 0 and wmape >= 0:
            print(f"  ✓ Metrics values are reasonable")
            return True
        else:
            print(f"  ✗ Metrics values are unreasonable")
            return False
        
    except Exception as e:
        print(f"  ✗ Metrics calculation test failed: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 80)
    print("Transformer+FFT Model Test Suite")
    print("=" * 80)
    
    tests = [
        ("Transformer Predictor", test_transformer_predictor),
        ("FFT Peak Predictor", test_fft_peak_predictor),
        ("Fusion Model", test_fusion_model),
        ("Data Compatibility", test_data_compatibility),
        ("Training Compatibility", test_training_compatibility),
        ("Metrics Calculation", test_metrics_calculation)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 50)
        result = test_func()
        results.append((test_name, result))
    
    # 总结
    print("\n" + "=" * 80)
    print("Test Results Summary:")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"  {test_name:<25}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The Transformer+FFT system is ready to use.")
        print("\nArchitecture Summary:")
        print("  ✅ Transformer: 处理完整的时间序列预测")
        print("  ✅ FFT: 专门处理高峰期频域预测")
        print("  ✅ Fusion: 结合两种预测结果")
        print("\nNext steps:")
        print("  1. Run 'python main_transformer_fft.py' to train and evaluate the models")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix the issues before proceeding.")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
