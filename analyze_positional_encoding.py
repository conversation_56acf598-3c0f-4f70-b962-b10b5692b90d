#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分析和可视化Transformer中的位置编码
展示位置编码的计算过程和可视化结果

Author: 008 - AI Algorithm Engineer for Subway Flow Prediction
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import math
import os

class PositionalEncoding(nn.Module):
    """位置编码模块，为序列中的每个位置添加位置信息"""
    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        self.register_buffer('pe', pe)

    def forward(self, x):
        # x: [batch_size, seq_len, d_model]
        x = x + self.pe[:, :x.size(1), :]
        return x

def analyze_positional_encoding():
    """分析位置编码的计算过程"""
    print("正在分析位置编码的计算过程...")
    
    # 使用工程中的配置参数
    d_model = 64  # 模型维度
    max_len = 20  # 分析前20个位置
    
    # 创建位置编码
    pos_encoder = PositionalEncoding(d_model, max_len)
    
    # 获取位置编码矩阵
    pe_matrix = pos_encoder.pe.squeeze(0)[:max_len, :].numpy()
    
    print(f"位置编码矩阵形状: {pe_matrix.shape}")
    print(f"d_model: {d_model}")
    print(f"分析位置数: {max_len}")
    
    return pe_matrix, d_model, max_len

def show_encoding_formula():
    """展示位置编码的数学公式"""
    print("\n位置编码数学公式:")
    print("=" * 60)
    print("PE(pos, 2i)   = sin(pos / 10000^(2i/d_model))")
    print("PE(pos, 2i+1) = cos(pos / 10000^(2i/d_model))")
    print("")
    print("其中:")
    print("- pos: 位置索引 (0, 1, 2, ...)")
    print("- i: 维度索引 (0, 1, 2, ..., d_model/2-1)")
    print("- 2i: 偶数维度使用sin函数")
    print("- 2i+1: 奇数维度使用cos函数")
    print("=" * 60)

def calculate_specific_examples(d_model=64):
    """计算具体位置的编码示例"""
    print(f"\n具体位置编码计算示例 (d_model={d_model}):")
    print("=" * 80)
    
    positions = [0, 1, 2]  # 对应工程中的3个时间维度
    
    for pos in positions:
        print(f"\n位置 {pos} 的编码计算:")
        print("-" * 40)
        
        # 计算前8个维度作为示例
        for i in range(4):  # i = 0, 1, 2, 3
            # 计算div_term
            div_term = math.exp(2 * i * (-math.log(10000.0) / d_model))
            
            # 计算sin和cos值
            sin_val = math.sin(pos * div_term)
            cos_val = math.cos(pos * div_term)
            
            print(f"维度 {2*i:2d} (sin): PE({pos}, {2*i:2d}) = sin({pos} * {div_term:.6f}) = {sin_val:8.6f}")
            print(f"维度 {2*i+1:2d} (cos): PE({pos}, {2*i+1:2d}) = cos({pos} * {div_term:.6f}) = {cos_val:8.6f}")

def demonstrate_encoding_addition():
    """演示位置编码与输入的相加过程"""
    print("\n位置编码与输入相加演示:")
    print("=" * 60)
    
    # 模拟输入数据
    batch_size = 1
    seq_len = 3  # 工程中的序列长度
    d_model = 64
    
    # 创建模拟输入 (经过input_projection后的数据)
    torch.manual_seed(42)
    input_data = torch.randn(batch_size, seq_len, d_model)
    
    # 创建位置编码
    pos_encoder = PositionalEncoding(d_model)
    
    # 应用位置编码
    encoded_data = pos_encoder(input_data)
    
    print(f"输入数据形状: {input_data.shape}")
    print(f"位置编码后形状: {encoded_data.shape}")
    
    # 显示前3个维度的具体数值
    print(f"\n位置0的前5个维度:")
    print(f"原始输入: {input_data[0, 0, :5].numpy()}")
    print(f"位置编码: {pos_encoder.pe[0, 0, :5].numpy()}")
    print(f"相加结果: {encoded_data[0, 0, :5].numpy()}")
    
    print(f"\n位置1的前5个维度:")
    print(f"原始输入: {input_data[0, 1, :5].numpy()}")
    print(f"位置编码: {pos_encoder.pe[0, 1, :5].numpy()}")
    print(f"相加结果: {encoded_data[0, 1, :5].numpy()}")
    
    print(f"\n位置2的前5个维度:")
    print(f"原始输入: {input_data[0, 2, :5].numpy()}")
    print(f"位置编码: {pos_encoder.pe[0, 2, :5].numpy()}")
    print(f"相加结果: {encoded_data[0, 2, :5].numpy()}")
    
    return input_data, encoded_data, pos_encoder

def plot_positional_encoding_heatmap(pe_matrix, d_model, max_len):
    """绘制位置编码热力图"""
    print("正在绘制位置编码热力图...")
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 子图1: 完整位置编码热力图
    ax1 = axes[0, 0]
    im1 = ax1.imshow(pe_matrix.T, cmap='RdBu', aspect='auto')
    ax1.set_title('Complete Positional Encoding Heatmap\n(All 64 Dimensions)', fontsize=12, fontweight='bold')
    ax1.set_xlabel('Position Index', fontsize=10)
    ax1.set_ylabel('Dimension Index', fontsize=10)
    ax1.set_xticks(range(0, max_len, 2))
    ax1.set_yticks(range(0, d_model, 8))
    plt.colorbar(im1, ax=ax1, shrink=0.8)
    
    # 子图2: 前16个维度的详细视图
    ax2 = axes[0, 1]
    im2 = ax2.imshow(pe_matrix[:, :16].T, cmap='RdBu', aspect='auto')
    ax2.set_title('First 16 Dimensions Detail\n(Positions 0-19)', fontsize=12, fontweight='bold')
    ax2.set_xlabel('Position Index', fontsize=10)
    ax2.set_ylabel('Dimension Index', fontsize=10)
    ax2.set_xticks(range(0, max_len, 2))
    ax2.set_yticks(range(0, 16, 2))
    plt.colorbar(im2, ax=ax2, shrink=0.8)
    
    # 子图3: 工程中实际使用的3个位置
    ax3 = axes[1, 0]
    engineering_positions = pe_matrix[:3, :16]  # 前3个位置，前16个维度
    im3 = ax3.imshow(engineering_positions.T, cmap='RdBu', aspect='auto')
    ax3.set_title('Engineering Usage: 3 Positions\n(Week, Day, Hour - First 16 Dims)', fontsize=12, fontweight='bold')
    ax3.set_xlabel('Position (0:Week, 1:Day, 2:Hour)', fontsize=10)
    ax3.set_ylabel('Dimension Index', fontsize=10)
    ax3.set_xticks([0, 1, 2])
    ax3.set_xticklabels(['Week', 'Day', 'Hour'])
    ax3.set_yticks(range(0, 16, 2))
    plt.colorbar(im3, ax=ax3, shrink=0.8)
    
    # 子图4: 位置编码的波形图
    ax4 = axes[1, 1]
    # 显示前8个维度的波形
    for dim in range(0, 8, 2):
        ax4.plot(range(max_len), pe_matrix[:, dim], label=f'Dim {dim} (sin)', alpha=0.7)
        ax4.plot(range(max_len), pe_matrix[:, dim+1], label=f'Dim {dim+1} (cos)', alpha=0.7, linestyle='--')
    
    ax4.set_title('Positional Encoding Waveforms\n(First 8 Dimensions)', fontsize=12, fontweight='bold')
    ax4.set_xlabel('Position Index', fontsize=10)
    ax4.set_ylabel('Encoding Value', fontsize=10)
    ax4.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if not os.path.exists('result'):
        os.makedirs('result')
    
    plt.savefig('result/positional_encoding_visualization.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("位置编码可视化图已保存到 result/positional_encoding_visualization.png")

def plot_encoding_comparison():
    """绘制位置编码前后的对比"""
    print("正在绘制位置编码前后对比...")
    
    # 创建模拟数据
    batch_size = 1
    seq_len = 3
    d_model = 64
    
    torch.manual_seed(42)
    input_data = torch.randn(batch_size, seq_len, d_model)
    
    pos_encoder = PositionalEncoding(d_model)
    encoded_data = pos_encoder(input_data)
    
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # 原始输入
    ax1 = axes[0]
    im1 = ax1.imshow(input_data[0].T.numpy(), cmap='viridis', aspect='auto')
    ax1.set_title('Original Input\n(After Linear Projection)', fontsize=12, fontweight='bold')
    ax1.set_xlabel('Position (0:Week, 1:Day, 2:Hour)', fontsize=10)
    ax1.set_ylabel('Dimension Index', fontsize=10)
    ax1.set_xticks([0, 1, 2])
    ax1.set_xticklabels(['Week', 'Day', 'Hour'])
    plt.colorbar(im1, ax=ax1, shrink=0.8)
    
    # 位置编码
    ax2 = axes[1]
    pe_values = pos_encoder.pe[0, :seq_len, :].numpy()
    im2 = ax2.imshow(pe_values.T, cmap='RdBu', aspect='auto')
    ax2.set_title('Positional Encoding\n(Added to Input)', fontsize=12, fontweight='bold')
    ax2.set_xlabel('Position (0:Week, 1:Day, 2:Hour)', fontsize=10)
    ax2.set_ylabel('Dimension Index', fontsize=10)
    ax2.set_xticks([0, 1, 2])
    ax2.set_xticklabels(['Week', 'Day', 'Hour'])
    plt.colorbar(im2, ax=ax2, shrink=0.8)
    
    # 编码后结果
    ax3 = axes[2]
    im3 = ax3.imshow(encoded_data[0].T.numpy(), cmap='plasma', aspect='auto')
    ax3.set_title('After Adding Positional Encoding\n(Input + Position)', fontsize=12, fontweight='bold')
    ax3.set_xlabel('Position (0:Week, 1:Day, 2:Hour)', fontsize=10)
    ax3.set_ylabel('Dimension Index', fontsize=10)
    ax3.set_xticks([0, 1, 2])
    ax3.set_xticklabels(['Week', 'Day', 'Hour'])
    plt.colorbar(im3, ax=ax3, shrink=0.8)
    
    plt.tight_layout()
    plt.savefig('result/positional_encoding_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("位置编码对比图已保存到 result/positional_encoding_comparison.png")

def save_detailed_analysis():
    """保存详细的位置编码分析"""
    print("正在保存详细分析...")
    
    if not os.path.exists('result'):
        os.makedirs('result')
    
    with open('result/positional_encoding_analysis.txt', 'w', encoding='utf-8') as f:
        f.write("Transformer位置编码详细分析\n")
        f.write("=" * 80 + "\n\n")
        
        f.write("1. 位置编码公式:\n")
        f.write("-" * 40 + "\n")
        f.write("PE(pos, 2i)   = sin(pos / 10000^(2i/d_model))\n")
        f.write("PE(pos, 2i+1) = cos(pos / 10000^(2i/d_model))\n\n")
        
        f.write("2. 工程中的配置:\n")
        f.write("-" * 40 + "\n")
        f.write("- d_model: 64 (模型维度)\n")
        f.write("- seq_len: 3 (序列长度: 周、天、时)\n")
        f.write("- max_len: 5000 (支持的最大序列长度)\n\n")
        
        f.write("3. 位置编码特点:\n")
        f.write("-" * 40 + "\n")
        f.write("- 偶数维度使用sin函数，奇数维度使用cos函数\n")
        f.write("- 不同维度有不同的频率，形成独特的位置模式\n")
        f.write("- 通过加法方式与输入特征融合\n")
        f.write("- 为每个位置提供唯一的编码向量\n\n")
        
        f.write("4. 在地铁预测中的作用:\n")
        f.write("-" * 40 + "\n")
        f.write("- 位置0: 周期性特征的位置编码\n")
        f.write("- 位置1: 日期性特征的位置编码\n")
        f.write("- 位置2: 时刻性特征的位置编码\n")
        f.write("- 帮助模型区分不同时间维度的特征\n")
    
    print("详细分析已保存到 result/positional_encoding_analysis.txt")

def main():
    """主函数"""
    print("=" * 80)
    print("Transformer位置编码分析和可视化")
    print("=" * 80)
    
    # 1. 展示编码公式
    show_encoding_formula()
    
    # 2. 分析位置编码
    pe_matrix, d_model, max_len = analyze_positional_encoding()
    
    # 3. 计算具体示例
    calculate_specific_examples(d_model)
    
    # 4. 演示编码相加过程
    input_data, encoded_data, pos_encoder = demonstrate_encoding_addition()
    
    # 5. 绘制位置编码热力图
    plot_positional_encoding_heatmap(pe_matrix, d_model, max_len)
    
    # 6. 绘制编码前后对比
    plot_encoding_comparison()
    
    # 7. 保存详细分析
    save_detailed_analysis()
    
    print("\n" + "=" * 80)
    print("位置编码分析完成！")
    print("生成文件:")
    print("- result/positional_encoding_visualization.png (位置编码热力图)")
    print("- result/positional_encoding_comparison.png (编码前后对比)")
    print("- result/positional_encoding_analysis.txt (详细分析)")
    print("=" * 80)

if __name__ == "__main__":
    main()
