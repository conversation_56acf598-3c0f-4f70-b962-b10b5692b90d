import numpy as np
import os
import time
import torch
import sys

# 添加当前目录到Python路径
if '.' not in sys.path:
    sys.path.append('.')

# 可选导入
try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    print("Warning: Matplotlib not available. Visualization will be disabled.")
    MATPLOTLIB_AVAILABLE = False

# 导入模块
try:
    from utils.utils import GetLaplacian
    from model.gcn_fft_fusion_model import GCN_FFT_FusionModel
    from data.get_inflow_only_dataloader import get_inflow_only_dataloader
    print("All modules imported successfully!")
except ImportError as e:
    print(f"Import error: {e}")
    print("Please check if all required files exist in the correct directories.")
    exit(1)

# 设备配置
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# 模型参数配置
time_interval = 15
time_lag = 10
tg_in_one_day = 72
forecast_day_number = 5
pre_len = 1
batch_size = 32
station_num = 276
peak_time_steps = 10

print("=" * 80)
print("GCN + FFT Fusion Metro Flow Prediction System - Prediction Mode")
print("基于GCN和FFT融合的地铁客流预测系统 - 预测模式")
print("=" * 80)
print("Architecture:")
print("  - GCN: 多尺度客流预测 (实时/日/周)")
print("  - FFT: 高峰期频域预测 (10个时间步)")
print("  - Fusion: 自适应权重融合")
print("=" * 80)

# 加载数据
print("Loading test data...")
_, _, inflow_data_loader_test, max_inflow, min_inflow = get_inflow_only_dataloader(
    time_interval=time_interval, 
    time_lag=time_lag, 
    tg_in_one_day=tg_in_one_day, 
    forecast_day_number=forecast_day_number, 
    pre_len=pre_len, 
    batch_size=batch_size
)

print(f"Data normalization - max_inflow: {max_inflow:.2f}, min_inflow: {min_inflow:.2f}")

# 加载邻接矩阵
print("Loading adjacency matrix...")
adjacency = np.loadtxt('./data/adjacency.csv', delimiter=",")
adjacency = torch.tensor(GetLaplacian(adjacency).get_normalized_adj(station_num)).type(torch.float32).to(device)

# 创建模型
print("Creating GCN+FFT Fusion model...")
model = GCN_FFT_FusionModel(time_lag, pre_len, station_num, device, peak_time_steps)
model = model.to(device)

# 加载训练好的模型
# 默认使用指定的训练好的融合模型路径
default_model_path = "D:/42972源代码/深度学习与交通大数据实战---源代码与数据集5/第4章_地铁代码 - transform优化了的版本 - 6/subway flow prediction/save_model/gcn_fft_fusion_2025_06_07_08_32_16/model_dict_checkpoint_8_0.00044315.pth"

model_path = input(f"Please enter the path to the trained fusion model (or press Enter to use default): ").strip()

if not model_path:
    # 使用默认的训练好的融合模型
    if os.path.exists(default_model_path):
        model_path = default_model_path
        print(f"Using default trained fusion model: {model_path}")
    else:
        print(f"Default model file not found: {default_model_path}")
        # 回退到自动寻找最新模型
        try:
            save_dirs = [d for d in os.listdir('./save_model/') if d.startswith('gcn_fft_fusion_')]
            if save_dirs:
                latest_dir = sorted(save_dirs)[-1]
                model_files = [f for f in os.listdir(f'./save_model/{latest_dir}/') if f.endswith('.pth')]
                if model_files:
                    model_path = f'./save_model/{latest_dir}/{model_files[0]}'
                    print(f"Using latest fusion model: {model_path}")
                else:
                    print("No model files found in the latest directory!")
                    exit(1)
            else:
                print("No trained fusion models found!")
                exit(1)
        except Exception as e:
            print(f"Error finding models: {e}")
            exit(1)

try:
    checkpoint = torch.load(model_path, map_location=device)
    model.load_state_dict(checkpoint)
    print(f"Fusion model loaded successfully from: {model_path}")
except Exception as e:
    print(f"Error loading model: {e}")
    exit(1)

# 预测阶段
print("\n" + "=" * 80)
print("Starting Prediction...")
print("=" * 80)

model.eval()
test_loss = 0
test_batches = 0
predictions = []
true_values = []
predictions_original = []
true_values_original = []

# 分别收集GCN和FFT的预测结果用于分析
gcn_predictions = []
fft_predictions = []

mse_loss = torch.nn.MSELoss().to(device)

start_time = time.time()

with torch.no_grad():
    for i_batch, (test_inflow_X, test_inflow_Y, test_inflow_Y_original) in enumerate(inflow_data_loader_test):
        test_inflow_X = test_inflow_X.type(torch.float32).to(device)
        test_inflow_Y = test_inflow_Y.type(torch.float32).to(device)
        
        # 融合模型预测
        prediction = model(test_inflow_X, adjacency)
        loss = mse_loss(prediction, test_inflow_Y)
        test_loss += loss.item()
        test_batches += 1
        
        # 单独获取GCN和FFT的预测结果用于分析
        gcn_pred = model.gcn_predictor(test_inflow_X, adjacency)
        peak_sequences = model.detect_peak_periods(test_inflow_X)
        fft_pred = model.fft_predictor(peak_sequences)
        
        # 调整FFT预测维度以匹配GCN
        # 确保FFT预测结果与GCN预测结果具有相同的维度
        if fft_pred.size(2) != model.pre_len:
            # 如果FFT输出是10个时间步，而pre_len是1，我们取平均值
            if model.pre_len == 1:
                fft_pred = fft_pred.mean(dim=2, keepdim=True)  # (batch_size, station_num, 1)
            else:
                # 使用线性插值调整到正确的预测长度
                fft_pred = torch.nn.functional.interpolate(
                    fft_pred.transpose(1, 2),
                    size=model.pre_len,
                    mode='linear',
                    align_corners=False
                ).transpose(1, 2)
        
        # 保存各种预测结果
        predictions.extend(prediction.cpu().numpy())
        true_values.extend(test_inflow_Y.cpu().numpy())
        gcn_predictions.extend(gcn_pred.cpu().numpy())
        fft_predictions.extend(fft_pred.cpu().numpy())
        
        # 反归一化到原始尺度
        prediction_original = prediction.cpu().numpy() * (max_inflow - min_inflow) + min_inflow
        true_original = test_inflow_Y_original.numpy()
        
        predictions_original.extend(prediction_original)
        true_values_original.extend(true_original)
        
        if (i_batch + 1) % 10 == 0:
            print(f"Processed {i_batch + 1}/{len(inflow_data_loader_test)} batches")

prediction_time = time.time() - start_time

# 计算测试损失
avg_test_loss = test_loss / test_batches
print(f"\nTest Loss: {avg_test_loss:.6f}")
print(f"Prediction time: {prediction_time:.2f} seconds")

# 转换为numpy数组
predictions_original = np.array(predictions_original)
true_values_original = np.array(true_values_original)
gcn_predictions = np.array(gcn_predictions)
fft_predictions = np.array(fft_predictions)

# 确保非负值
predictions_original = np.maximum(predictions_original, 0)
true_values_original = np.maximum(true_values_original, 0)

print(f"Fusion predictions shape: {predictions_original.shape}")
print(f"True values shape: {true_values_original.shape}")
print(f"GCN predictions shape: {gcn_predictions.shape}")
print(f"FFT predictions shape: {fft_predictions.shape}")

# 计算评估指标
print("\n" + "=" * 80)
print("Calculating Evaluation Metrics...")
print("=" * 80)

# 重塑数据用于计算指标
pred_flat = predictions_original.reshape(-1)
true_flat = true_values_original.reshape(-1)
gcn_flat = (gcn_predictions * (max_inflow - min_inflow) + min_inflow).reshape(-1)
fft_flat = (fft_predictions * (max_inflow - min_inflow) + min_inflow).reshape(-1)

# 计算各种指标
from sklearn.metrics import mean_squared_error, mean_absolute_error

# 融合模型指标
fusion_rmse = np.sqrt(mean_squared_error(true_flat, pred_flat))
fusion_mae = mean_absolute_error(true_flat, pred_flat)
fusion_mape = np.mean(np.abs((true_flat - pred_flat) / (true_flat + 1e-8))) * 100
fusion_wmape = np.sum(np.abs(true_flat - pred_flat)) / np.sum(true_flat) * 100

# GCN模型指标
gcn_rmse = np.sqrt(mean_squared_error(true_flat, gcn_flat))
gcn_mae = mean_absolute_error(true_flat, gcn_flat)

# FFT模型指标
fft_rmse = np.sqrt(mean_squared_error(true_flat, fft_flat))
fft_mae = mean_absolute_error(true_flat, fft_flat)

print(f"Fusion Model Performance:")
print(f"  RMSE: {fusion_rmse:.4f}")
print(f"  MAE:  {fusion_mae:.4f}")
print(f"  MAPE: {fusion_mape:.4f}%")
print(f"  WMAPE: {fusion_wmape:.4f}%")

print(f"\nGCN Component Performance:")
print(f"  RMSE: {gcn_rmse:.4f}")
print(f"  MAE:  {gcn_mae:.4f}")

print(f"\nFFT Component Performance:")
print(f"  RMSE: {fft_rmse:.4f}")
print(f"  MAE:  {fft_mae:.4f}")

# 保存结果
result_dir = 'result'
os.makedirs(result_dir, exist_ok=True)

# 保存预测结果
timestamp = time.strftime("%Y%m%d_%H%M%S")
fusion_pred_file = f'{result_dir}/gcn_fft_fusion_predictions_{timestamp}.txt'
true_file = f'{result_dir}/gcn_fft_fusion_true_values_{timestamp}.txt'
gcn_pred_file = f'{result_dir}/gcn_component_predictions_{timestamp}.txt'
fft_pred_file = f'{result_dir}/fft_component_predictions_{timestamp}.txt'
metrics_file = f'{result_dir}/gcn_fft_fusion_metrics_{timestamp}.txt'

np.savetxt(fusion_pred_file, predictions_original.reshape(-1, station_num), fmt='%.2f')
np.savetxt(true_file, true_values_original.reshape(-1, station_num), fmt='%.2f')
np.savetxt(gcn_pred_file, gcn_flat.reshape(-1, station_num), fmt='%.2f')
np.savetxt(fft_pred_file, fft_flat.reshape(-1, station_num), fmt='%.2f')

# 保存评估指标
with open(metrics_file, 'w') as f:
    f.write("GCN+FFT Fusion Model Evaluation Metrics\n")
    f.write("=" * 50 + "\n")
    f.write(f"Model Path: {model_path}\n")
    f.write(f"Test Loss: {avg_test_loss:.6f}\n")
    f.write(f"Prediction Time: {prediction_time:.2f} seconds\n\n")
    
    f.write("Fusion Model Performance:\n")
    f.write(f"  RMSE: {fusion_rmse:.4f}\n")
    f.write(f"  MAE: {fusion_mae:.4f}\n")
    f.write(f"  MAPE: {fusion_mape:.4f}%\n")
    f.write(f"  WMAPE: {fusion_wmape:.4f}%\n\n")
    
    f.write("GCN Component Performance:\n")
    f.write(f"  RMSE: {gcn_rmse:.4f}\n")
    f.write(f"  MAE: {gcn_mae:.4f}\n\n")
    
    f.write("FFT Component Performance:\n")
    f.write(f"  RMSE: {fft_rmse:.4f}\n")
    f.write(f"  MAE: {fft_mae:.4f}\n\n")
    
    f.write(f"Data Shape: {predictions_original.shape}\n")

print(f"\nResults saved:")
print(f"  - Fusion predictions: {fusion_pred_file}")
print(f"  - GCN predictions: {gcn_pred_file}")
print(f"  - FFT predictions: {fft_pred_file}")
print(f"  - True values: {true_file}")
print(f"  - Metrics: {metrics_file}")

# 可视化部分结果
if MATPLOTLIB_AVAILABLE:
    print("\n" + "=" * 80)
    print("Generating Visualization...")
    print("=" * 80)

    # 选择前5个车站进行可视化
    selected_stations = [0, 50, 100, 150, 200]
    num_samples = min(50, predictions_original.shape[0])

    plt.figure(figsize=(20, 12))
    for i, station_idx in enumerate(selected_stations):
        plt.subplot(2, 3, i+1)
        plt.plot(true_values_original[:num_samples, station_idx, 0], 'b-', label='True', linewidth=2)
        plt.plot(predictions_original[:num_samples, station_idx, 0], 'r--', label='Fusion', linewidth=2)
        plt.plot(gcn_flat.reshape(-1, station_num)[:num_samples, station_idx], 'g:', label='GCN', linewidth=1.5)
        plt.plot(fft_flat.reshape(-1, station_num)[:num_samples, station_idx], 'm:', label='FFT', linewidth=1.5)
        plt.title(f'Station {station_idx} - Fusion vs Components')
        plt.xlabel('Time Steps')
        plt.ylabel('Flow')
        plt.legend()
        plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plot_file = f'{result_dir}/gcn_fft_fusion_comparison_{timestamp}.png'
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    print(f"Visualization saved: {plot_file}")
else:
    print("\nVisualization skipped (matplotlib not available)")

print("\n" + "=" * 80)
print("GCN+FFT Fusion Prediction Completed Successfully!")
print("=" * 80)
print("Summary:")
print("  ✅ GCN: 处理多尺度客流预测")
print("  ✅ FFT: 处理高峰期频域预测")
print("  ✅ Fusion: 自适应权重融合")
print("  ✅ 完整的性能评估和可视化")
print("=" * 80)
