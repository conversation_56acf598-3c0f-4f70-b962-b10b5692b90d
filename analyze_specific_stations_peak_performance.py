#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于analyze_main_predict_on_fft_peak_sequences.py的方法
统计车站30,14,95,97,262,33的数据集后五天高峰10个时间步的总RMSE、MAE、WMAPE值

Author: 008 - AI Algorithm Engineer for Subway Flow Prediction
"""

import numpy as np
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import os

def load_main_predict_results():
    """加载main_predict的预测结果"""
    print("正在加载main_predict的预测结果...")
    
    # 加载预测值 (276个车站, N个时间步)
    predictions = np.loadtxt('result/improved_ALL_276_stations_predictions.txt')
    print(f"预测值形状: {predictions.shape}")
    
    # 加载真实值 (276个车站, N个时间步)
    true_values = np.loadtxt('result/improved_ALL_276_stations_original.txt')
    print(f"真实值形状: {true_values.shape}")
    
    return predictions, true_values

def load_fft_peak_times_and_mapping():
    """加载FFT高峰时刻信息和时间映射"""
    print("正在加载FFT高峰时刻信息...")
    
    # 加载高峰时刻信息
    peak_times = np.loadtxt('./data/continuous_peak_flow_dataset_peak_times.csv', delimiter=',', dtype=int)
    print(f"高峰时刻数据形状: {peak_times.shape}")
    
    # 数据集参数
    total_days = 25
    time_steps_per_day = 72
    test_days = 5  # 最后5天为测试集
    train_val_days = 20  # 前20天为训练+验证集
    sequence_length = 10  # 高峰时刻左右10个时间步
    
    print(f"数据集信息:")
    print(f"- 总天数: {total_days}")
    print(f"- 每天时间步: {time_steps_per_day}")
    print(f"- 训练+验证天数: {train_val_days}")
    print(f"- 测试天数: {test_days}")
    print(f"- 高峰序列长度: {sequence_length}")
    
    return peak_times, train_val_days, test_days, time_steps_per_day, sequence_length

def map_fft_peak_sequences_to_main_predict_test_set(peak_times, train_val_days, test_days, 
                                                   time_steps_per_day, sequence_length, target_stations):
    """将指定车站的FFT高峰时刻序列映射到main_predict的测试集时间索引"""
    print(f"正在映射指定车站{target_stations}的FFT高峰时刻序列到main_predict测试集...")
    
    # main_predict的测试集对应原始数据的最后5天
    test_start_day = train_val_days  # 第21天开始
    
    station_peak_sequence_indices = {}
    
    for station_idx in target_stations:
        station_sequence_indices = []
        peak_time = peak_times[station_idx]
        
        print(f"车站{station_idx}的高峰时刻: {peak_time} (第{peak_time//4}小时{(peak_time%4)*15}分)")
        
        # 对测试集的每一天，找到对应的高峰时刻序列
        for test_day in range(test_days):
            actual_day = test_start_day + test_day  # 第21-25天
            
            # 计算该天该车站高峰时刻在原始数据中的绝对索引
            day_start_index = actual_day * time_steps_per_day
            peak_center_index = day_start_index + peak_time
            
            # 计算高峰时刻左右10个时间步的索引范围
            # 以高峰时刻为中心，前4个+高峰时刻+后5个 = 10个时间步
            sequence_start = peak_center_index - 4
            sequence_end = peak_center_index + 6  # 不包含end，所以是+6
            
            # 确保序列在有效范围内
            sequence_start = max(sequence_start, day_start_index)
            sequence_end = min(sequence_end, day_start_index + time_steps_per_day)
            
            # 转换为main_predict测试集中的相对索引
            test_set_start_index = train_val_days * time_steps_per_day
            
            day_sequence_indices = []
            for abs_idx in range(sequence_start, sequence_end):
                relative_idx = abs_idx - test_set_start_index
                if 0 <= relative_idx < 359:  # 确保在测试集范围内
                    day_sequence_indices.append(relative_idx)
            
            # 如果序列长度不足10，进行填充
            while len(day_sequence_indices) < sequence_length:
                if len(day_sequence_indices) > 0:
                    # 用最后一个有效索引填充
                    day_sequence_indices.append(day_sequence_indices[-1])
                else:
                    # 如果完全没有有效索引，用0填充
                    day_sequence_indices.append(0)
            
            # 如果序列长度超过10，截取前10个
            day_sequence_indices = day_sequence_indices[:sequence_length]
            
            station_sequence_indices.extend(day_sequence_indices)
        
        station_peak_sequence_indices[station_idx] = station_sequence_indices
        print(f"车站{station_idx}在测试集中有{len(station_sequence_indices)}个高峰时间步")
    
    return station_peak_sequence_indices

def extract_station_peak_sequence_data(predictions, true_values, station_peak_sequence_indices, target_stations):
    """提取指定车站高峰时刻序列的预测值和真实值"""
    print("正在提取指定车站高峰时刻序列的预测值和真实值...")
    
    all_predictions = []
    all_true_values = []
    station_data = {}
    
    for station_idx in target_stations:
        station_sequence_indices = station_peak_sequence_indices[station_idx]
        
        station_predictions = []
        station_true_values = []
        
        for seq_idx in station_sequence_indices:
            # 确保索引在有效范围内
            if 0 <= seq_idx < predictions.shape[1] and 0 <= seq_idx < true_values.shape[1]:
                pred_val = predictions[station_idx, seq_idx]
                true_val = true_values[station_idx, seq_idx]
                
                station_predictions.append(pred_val)
                station_true_values.append(true_val)
                all_predictions.append(pred_val)
                all_true_values.append(true_val)
            else:
                print(f"警告: 车站{station_idx}的序列索引{seq_idx}超出范围")
                # 使用边界值
                if seq_idx < 0:
                    pred_val = predictions[station_idx, 0]
                    true_val = true_values[station_idx, 0]
                else:
                    pred_val = predictions[station_idx, -1]
                    true_val = true_values[station_idx, -1]
                
                station_predictions.append(pred_val)
                station_true_values.append(true_val)
                all_predictions.append(pred_val)
                all_true_values.append(true_val)
        
        station_data[station_idx] = {
            'predictions': np.array(station_predictions),
            'true_values': np.array(station_true_values),
            'count': len(station_predictions)
        }
        
        print(f"车站{station_idx}: 提取了{len(station_predictions)}个高峰时间步数据")
    
    all_predictions = np.array(all_predictions)
    all_true_values = np.array(all_true_values)
    
    print(f"总计提取了{len(all_predictions)}个高峰时间步数据点")
    print(f"预期数量: {len(target_stations) * 5 * 10} ({len(target_stations)}个车站 × 5个测试天 × 10个时间步)")
    
    return all_predictions, all_true_values, station_data

def calculate_metrics(predictions, true_values):
    """计算评估指标"""
    # 计算RMSE
    rmse = np.sqrt(mean_squared_error(true_values, predictions))
    
    # 计算MAE
    mae = mean_absolute_error(true_values, predictions)
    
    # 计算WMAPE
    mask = true_values > 0
    if np.sum(mask) > 0:
        wmape = np.sum(np.abs(predictions[mask] - true_values[mask])) / np.sum(true_values[mask])
    else:
        wmape = 0
    
    # 计算R2
    r2 = r2_score(true_values, predictions)
    
    return rmse, mae, wmape, r2

def calculate_station_metrics(station_data, target_stations):
    """计算各车站的评估指标"""
    print("正在计算各车站的评估指标...")
    
    station_metrics = {}
    
    for station_idx in target_stations:
        data = station_data[station_idx]
        predictions = data['predictions']
        true_values = data['true_values']
        
        if len(predictions) > 0:
            rmse, mae, wmape, r2 = calculate_metrics(predictions, true_values)
            
            station_metrics[station_idx] = {
                'rmse': rmse,
                'mae': mae,
                'wmape': wmape,
                'r2': r2,
                'count': len(predictions),
                'pred_mean': np.mean(predictions),
                'true_mean': np.mean(true_values),
                'pred_range': [np.min(predictions), np.max(predictions)],
                'true_range': [np.min(true_values), np.max(true_values)]
            }
            
            print(f"车站{station_idx}: RMSE={rmse:.4f}, MAE={mae:.4f}, WMAPE={wmape:.4f}")
    
    return station_metrics

def save_results(target_stations, overall_rmse, overall_mae, overall_wmape, overall_r2,
                station_metrics, all_predictions, all_true_values):
    """保存分析结果"""
    if not os.path.exists('result'):
        os.makedirs('result')
    
    # 保存结果
    with open('result/specific_stations_peak_performance.txt', 'w', encoding='utf-8') as f:
        f.write("指定车站高峰期10个时间步预测性能分析\n")
        f.write("=" * 80 + "\n\n")
        
        f.write("分析说明:\n")
        f.write("-" * 40 + "\n")
        f.write(f"- 分析车站: {target_stations}\n")
        f.write("- 基于analyze_main_predict_on_fft_peak_sequences.py方法\n")
        f.write("- 数据来源: main_predict_improved.py的预测结果\n")
        f.write("- 评估范围: 测试集(最后5天)的高峰时刻左右10个时间步\n")
        f.write("- 每个车站: 10个时间步 × 5天 = 50个预测值\n")
        f.write(f"- 总计: {len(target_stations)}个车站 × 50 = {len(all_predictions)}个预测值\n\n")
        
        f.write("整体评估指标(指定车站高峰时刻序列):\n")
        f.write("-" * 40 + "\n")
        f.write(f"总RMSE: {overall_rmse:.4f}\n")
        f.write(f"总MAE: {overall_mae:.4f}\n")
        f.write(f"总WMAPE: {overall_wmape:.4f} ({overall_wmape*100:.2f}%)\n")
        f.write(f"总R2: {overall_r2:.4f}\n\n")
        
        f.write("各车站详细指标:\n")
        f.write("-" * 80 + "\n")
        f.write("车站ID | RMSE     | MAE      | WMAPE    | R2       | 数据点数 | 预测均值 | 真实均值\n")
        f.write("-" * 80 + "\n")
        
        for station_idx in target_stations:
            if station_idx in station_metrics:
                metrics = station_metrics[station_idx]
                f.write(f"{station_idx:6d} | {metrics['rmse']:8.4f} | {metrics['mae']:8.4f} | "
                       f"{metrics['wmape']:8.4f} | {metrics['r2']:8.4f} | {metrics['count']:8d} | "
                       f"{metrics['pred_mean']:8.2f} | {metrics['true_mean']:8.2f}\n")
        
        f.write("\n数据统计:\n")
        f.write("-" * 40 + "\n")
        f.write(f"总预测值范围: [{np.min(all_predictions):.2f}, {np.max(all_predictions):.2f}]\n")
        f.write(f"总真实值范围: [{np.min(all_true_values):.2f}, {np.max(all_true_values):.2f}]\n")
        f.write(f"总预测值平均: {np.mean(all_predictions):.2f}\n")
        f.write(f"总真实值平均: {np.mean(all_true_values):.2f}\n")
    
    print("分析结果已保存到 result/specific_stations_peak_performance.txt")

def main():
    """主函数"""
    print("=" * 80)
    print("指定车站高峰期10个时间步预测性能分析")
    print("基于analyze_main_predict_on_fft_peak_sequences.py方法")
    print("=" * 80)
    
    # 指定的车站
    target_stations = [30, 14, 95, 97, 262, 33]
    print(f"分析车站: {target_stations}")
    
    # 1. 加载main_predict的预测结果
    predictions, true_values = load_main_predict_results()
    
    # 2. 加载FFT高峰时刻信息
    peak_times, train_val_days, test_days, time_steps_per_day, sequence_length = load_fft_peak_times_and_mapping()
    
    # 3. 映射指定车站的FFT高峰时刻序列到main_predict测试集
    station_peak_sequence_indices = map_fft_peak_sequences_to_main_predict_test_set(
        peak_times, train_val_days, test_days, time_steps_per_day, sequence_length, target_stations)
    
    # 4. 提取指定车站高峰时刻序列的预测值和真实值
    all_predictions, all_true_values, station_data = extract_station_peak_sequence_data(
        predictions, true_values, station_peak_sequence_indices, target_stations)
    
    # 5. 计算整体评估指标
    overall_rmse, overall_mae, overall_wmape, overall_r2 = calculate_metrics(
        all_predictions, all_true_values)
    
    # 6. 计算各车站级别的指标
    station_metrics = calculate_station_metrics(station_data, target_stations)
    
    # 7. 输出结果
    print("\n" + "=" * 80)
    print(f"车站{target_stations}高峰期10个时间步预测性能:")
    print("-" * 80)
    print(f"总数据点数: {len(all_predictions)} ({len(target_stations)}个车站 × 5天 × 10时间步)")
    print(f"总RMSE: {overall_rmse:.4f}")
    print(f"总MAE: {overall_mae:.4f}")
    print(f"总WMAPE: {overall_wmape:.4f} ({overall_wmape*100:.2f}%)")
    print(f"总R2: {overall_r2:.4f}")
    print("=" * 80)
    
    # 8. 保存详细结果
    save_results(target_stations, overall_rmse, overall_mae, overall_wmape, overall_r2,
                station_metrics, all_predictions, all_true_values)
    
    print("\n分析完成！")
    print("这些指标反映了指定车站在FFT选定的高峰时刻序列的预测准确性。")

if __name__ == "__main__":
    main()
