#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
计算所有车站用GCN+Transformer和FFT预测的测试集后五天高峰期10个时间步的RMSE、MAE、WMAPE
找出FFT预测的RMSE值比GCN+Transformer预测的RMSE值小的车站

Author: 008 - AI Algorithm Engineer for Subway Flow Prediction
"""

import numpy as np
import pandas as pd
from sklearn.metrics import mean_squared_error, mean_absolute_error
import os

def load_fft_results():
    """加载FFT预测结果"""
    print("正在加载FFT预测结果...")
    
    fft_metrics = {}
    
    try:
        with open('result/fft_detailed_metrics.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
            # 找到车站详细指标部分
            start_idx = None
            for i, line in enumerate(lines):
                if "车站ID | RMSE" in line:
                    start_idx = i + 2  # 跳过分隔线
                    break
            
            if start_idx:
                for i in range(start_idx, len(lines)):
                    line = lines[i].strip()
                    if line and '|' in line:
                        parts = line.split('|')
                        if len(parts) >= 4:
                            try:
                                station_id = int(parts[0].strip())
                                rmse = float(parts[1].strip())
                                mae = float(parts[2].strip())
                                wmape = float(parts[3].strip())
                                
                                fft_metrics[station_id] = {
                                    'rmse': rmse,
                                    'mae': mae,
                                    'wmape': wmape
                                }
                            except ValueError:
                                continue
        
        print(f"成功加载 {len(fft_metrics)} 个车站的FFT指标")
        return fft_metrics
        
    except Exception as e:
        print(f"加载FFT指标失败: {e}")
        return {}

def load_main_model_results():
    """加载主模型(GCN+Transformer)结果"""
    print("正在加载主模型结果...")
    
    try:
        # 加载主模型的完整预测结果
        main_predictions = np.loadtxt('result/improved_ALL_276_stations_predictions.txt')
        main_true_values = np.loadtxt('result/improved_ALL_276_stations_original.txt')
        
        print(f"主模型数据形状: 预测{main_predictions.shape}, 真实{main_true_values.shape}")
        return main_predictions, main_true_values
        
    except Exception as e:
        print(f"加载主模型结果失败: {e}")
        return None, None

def extract_peak_sequences_for_all_stations(main_predictions, main_true_values):
    """为所有车站提取高峰期序列数据"""
    print("正在提取所有车站的高峰期序列数据...")
    
    # 加载高峰时刻信息
    peak_times = np.loadtxt('./data/continuous_peak_flow_dataset_peak_times.csv', delimiter=',', dtype=int)
    
    # 假设测试集有5天数据，每天72个时间步
    test_days = 5
    steps_per_day = 72
    train_val_days = 20  # 前20天为训练+验证集
    
    all_station_data = {}
    
    for station_id in range(276):  # 所有276个车站
        # 获取该车站的高峰时刻
        peak_time = peak_times[station_id]
        
        # 提取5天的高峰序列，每天10个时间步
        main_pred_sequences = []
        main_true_sequences = []
        
        for day in range(test_days):
            # 计算该天的高峰时刻在测试集中的位置
            day_start = day * steps_per_day
            peak_start = day_start + peak_time - 4  # 高峰时刻前4步
            peak_end = peak_start + 10  # 10个时间步
            
            # 确保索引在有效范围内
            peak_start = max(0, min(peak_start, main_predictions.shape[1] - 10))
            peak_end = peak_start + 10
            
            main_pred_sequences.extend(main_predictions[station_id, peak_start:peak_end])
            main_true_sequences.extend(main_true_values[station_id, peak_start:peak_end])
        
        main_pred_seq = np.array(main_pred_sequences)
        main_true_seq = np.array(main_true_sequences)
        
        # 确保序列长度为50
        if len(main_pred_seq) > 50:
            main_pred_seq = main_pred_seq[:50]
            main_true_seq = main_true_seq[:50]
        elif len(main_pred_seq) < 50:
            # 填充到50个点
            padding_length = 50 - len(main_pred_seq)
            main_pred_seq = np.pad(main_pred_seq, (0, padding_length), 'edge')
            main_true_seq = np.pad(main_true_seq, (0, padding_length), 'edge')
        
        all_station_data[station_id] = {
            'main_pred': main_pred_seq,
            'main_true': main_true_seq,
            'peak_time': peak_time
        }
    
    print(f"成功提取 {len(all_station_data)} 个车站的高峰期序列数据")
    return all_station_data

def calculate_metrics(y_true, y_pred):
    """计算RMSE、MAE、WMAPE指标"""
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    mae = mean_absolute_error(y_true, y_pred)
    
    # 计算WMAPE，避免除零错误
    wmape = np.sum(np.abs(y_true - y_pred)) / (np.sum(np.abs(y_true)) + 1e-8)
    
    return rmse, mae, wmape

def calculate_gcn_transformer_metrics(all_station_data):
    """计算所有车站的GCN+Transformer指标"""
    print("正在计算所有车站的GCN+Transformer指标...")
    
    gcn_transformer_metrics = {}
    
    for station_id in range(276):
        if station_id in all_station_data:
            data = all_station_data[station_id]
            predictions = data['main_pred']
            true_values = data['main_true']
            
            rmse, mae, wmape = calculate_metrics(true_values, predictions)
            
            gcn_transformer_metrics[station_id] = {
                'rmse': rmse,
                'mae': mae,
                'wmape': wmape,
                'peak_time': data['peak_time']
            }
    
    print(f"成功计算 {len(gcn_transformer_metrics)} 个车站的GCN+Transformer指标")
    return gcn_transformer_metrics

def compare_methods_and_find_fft_better_stations(fft_metrics, gcn_transformer_metrics):
    """比较两种方法并找出FFT的RMSE更小的车站"""
    print("正在比较两种方法并找出FFT的RMSE更小的车站...")
    
    comparison_results = []
    fft_better_stations = []
    
    for station_id in range(276):
        if station_id in fft_metrics and station_id in gcn_transformer_metrics:
            fft_data = fft_metrics[station_id]
            gcn_data = gcn_transformer_metrics[station_id]
            
            # 比较RMSE
            fft_rmse = fft_data['rmse']
            gcn_rmse = gcn_data['rmse']
            rmse_improvement = gcn_rmse - fft_rmse
            rmse_improvement_pct = (rmse_improvement / gcn_rmse) * 100 if gcn_rmse > 0 else 0
            
            # 比较MAE
            fft_mae = fft_data['mae']
            gcn_mae = gcn_data['mae']
            mae_improvement = gcn_mae - fft_mae
            mae_improvement_pct = (mae_improvement / gcn_mae) * 100 if gcn_mae > 0 else 0
            
            # 比较WMAPE
            fft_wmape = fft_data['wmape']
            gcn_wmape = gcn_data['wmape']
            wmape_improvement = gcn_wmape - fft_wmape
            wmape_improvement_pct = (wmape_improvement / gcn_wmape) * 100 if gcn_wmape > 0 else 0
            
            result = {
                'station_id': station_id,
                'peak_time': gcn_data['peak_time'],
                'fft_rmse': fft_rmse,
                'gcn_rmse': gcn_rmse,
                'fft_mae': fft_mae,
                'gcn_mae': gcn_mae,
                'fft_wmape': fft_wmape,
                'gcn_wmape': gcn_wmape,
                'rmse_improvement': rmse_improvement,
                'rmse_improvement_pct': rmse_improvement_pct,
                'mae_improvement': mae_improvement,
                'mae_improvement_pct': mae_improvement_pct,
                'wmape_improvement': wmape_improvement,
                'wmape_improvement_pct': wmape_improvement_pct,
                'fft_rmse_better': fft_rmse < gcn_rmse
            }
            
            comparison_results.append(result)
            
            # 如果FFT的RMSE更小，加入到优势车站列表
            if fft_rmse < gcn_rmse:
                fft_better_stations.append(result)
    
    print(f"总共比较了 {len(comparison_results)} 个车站")
    print(f"FFT的RMSE更小的车站数量: {len(fft_better_stations)}")
    
    return comparison_results, fft_better_stations

def save_comparison_results(comparison_results, fft_better_stations):
    """保存比较结果"""
    print("正在保存比较结果...")
    
    if not os.path.exists('result'):
        os.makedirs('result')
    
    # 保存所有车站的比较结果
    with open('result/all_stations_fft_vs_gcn_transformer_comparison.txt', 'w', encoding='utf-8') as f:
        f.write("所有车站FFT vs GCN+Transformer预测性能比较\n")
        f.write("=" * 100 + "\n\n")
        
        f.write("比较说明:\n")
        f.write("- 评估范围: 测试集后5天高峰期10个时间步\n")
        f.write("- 数据点数: 每个车站50个数据点 (5天×10时间步)\n")
        f.write("- 比较指标: RMSE、MAE、WMAPE\n")
        f.write("- 改进值: GCN+Transformer值 - FFT值 (正值表示FFT更好)\n\n")
        
        f.write("所有车站比较结果:\n")
        f.write("-" * 100 + "\n")
        f.write(f"{'车站ID':<6} {'高峰时刻':<8} {'FFT_RMSE':<10} {'GCN_RMSE':<10} {'FFT_MAE':<10} {'GCN_MAE':<10} {'FFT_WMAPE':<12} {'GCN_WMAPE':<12} {'RMSE改进%':<10}\n")
        f.write("-" * 100 + "\n")
        
        for result in comparison_results:
            peak_time = result['peak_time']
            hour = peak_time // 4
            minute = (peak_time % 4) * 15
            
            f.write(f"{result['station_id']:<6} {hour:02d}:{minute:02d}     "
                   f"{result['fft_rmse']:<10.4f} {result['gcn_rmse']:<10.4f} "
                   f"{result['fft_mae']:<10.4f} {result['gcn_mae']:<10.4f} "
                   f"{result['fft_wmape']:<12.4f} {result['gcn_wmape']:<12.4f} "
                   f"{result['rmse_improvement_pct']:<10.2f}\n")
        
        # 统计信息
        fft_rmse_better_count = sum(1 for r in comparison_results if r['fft_rmse_better'])
        fft_mae_better_count = sum(1 for r in comparison_results if r['fft_mae'] < r['gcn_mae'])
        fft_wmape_better_count = sum(1 for r in comparison_results if r['fft_wmape'] < r['gcn_wmape'])
        
        f.write(f"\n统计信息:\n")
        f.write("-" * 50 + "\n")
        f.write(f"总车站数: {len(comparison_results)}\n")
        f.write(f"FFT的RMSE更小的车站数: {fft_rmse_better_count} ({fft_rmse_better_count/len(comparison_results)*100:.1f}%)\n")
        f.write(f"FFT的MAE更小的车站数: {fft_mae_better_count} ({fft_mae_better_count/len(comparison_results)*100:.1f}%)\n")
        f.write(f"FFT的WMAPE更小的车站数: {fft_wmape_better_count} ({fft_wmape_better_count/len(comparison_results)*100:.1f}%)\n")
    
    # 保存FFT优势车站的详细信息
    with open('result/fft_better_rmse_stations.txt', 'w', encoding='utf-8') as f:
        f.write("FFT的RMSE比GCN+Transformer更小的车站详细信息\n")
        f.write("=" * 80 + "\n\n")
        
        f.write(f"找到 {len(fft_better_stations)} 个FFT的RMSE更小的车站\n\n")
        
        if len(fft_better_stations) > 0:
            f.write("详细信息:\n")
            f.write("-" * 80 + "\n")
            f.write(f"{'车站ID':<6} {'高峰时刻':<8} {'FFT_RMSE':<10} {'GCN_RMSE':<10} {'FFT_MAE':<10} {'GCN_MAE':<10} {'FFT_WMAPE':<12} {'GCN_WMAPE':<12}\n")
            f.write("-" * 80 + "\n")
            
            # 按RMSE改进幅度排序
            fft_better_stations_sorted = sorted(fft_better_stations, 
                                               key=lambda x: x['rmse_improvement_pct'], 
                                               reverse=True)
            
            for result in fft_better_stations_sorted:
                peak_time = result['peak_time']
                hour = peak_time // 4
                minute = (peak_time % 4) * 15
                
                f.write(f"{result['station_id']:<6} {hour:02d}:{minute:02d}     "
                       f"{result['fft_rmse']:<10.4f} {result['gcn_rmse']:<10.4f} "
                       f"{result['fft_mae']:<10.4f} {result['gcn_mae']:<10.4f} "
                       f"{result['fft_wmape']:<12.4f} {result['gcn_wmape']:<12.4f}\n")
            
            # 计算FFT优势车站的平均性能
            avg_fft_rmse = np.mean([r['fft_rmse'] for r in fft_better_stations])
            avg_gcn_rmse = np.mean([r['gcn_rmse'] for r in fft_better_stations])
            avg_fft_mae = np.mean([r['fft_mae'] for r in fft_better_stations])
            avg_gcn_mae = np.mean([r['gcn_mae'] for r in fft_better_stations])
            avg_fft_wmape = np.mean([r['fft_wmape'] for r in fft_better_stations])
            avg_gcn_wmape = np.mean([r['gcn_wmape'] for r in fft_better_stations])
            
            f.write(f"\nFFT优势车站平均性能:\n")
            f.write("-" * 40 + "\n")
            f.write(f"平均FFT RMSE: {avg_fft_rmse:.4f}\n")
            f.write(f"平均GCN RMSE: {avg_gcn_rmse:.4f}\n")
            f.write(f"平均FFT MAE: {avg_fft_mae:.4f}\n")
            f.write(f"平均GCN MAE: {avg_gcn_mae:.4f}\n")
            f.write(f"平均FFT WMAPE: {avg_fft_wmape:.4f} ({avg_fft_wmape*100:.2f}%)\n")
            f.write(f"平均GCN WMAPE: {avg_gcn_wmape:.4f} ({avg_gcn_wmape*100:.2f}%)\n")
            
            f.write(f"\n平均改进幅度:\n")
            f.write("-" * 40 + "\n")
            avg_rmse_improvement = np.mean([r['rmse_improvement_pct'] for r in fft_better_stations])
            avg_mae_improvement = np.mean([r['mae_improvement_pct'] for r in fft_better_stations])
            avg_wmape_improvement = np.mean([r['wmape_improvement_pct'] for r in fft_better_stations])
            
            f.write(f"平均RMSE改进: {avg_rmse_improvement:.2f}%\n")
            f.write(f"平均MAE改进: {avg_mae_improvement:.2f}%\n")
            f.write(f"平均WMAPE改进: {avg_wmape_improvement:.2f}%\n")
        else:
            f.write("没有找到FFT的RMSE更小的车站。\n")
    
    print("比较结果已保存到:")
    print("- result/all_stations_fft_vs_gcn_transformer_comparison.txt")
    print("- result/fft_better_rmse_stations.txt")

def main():
    """主函数"""
    print("=" * 80)
    print("计算所有车站FFT vs GCN+Transformer预测性能比较")
    print("找出FFT的RMSE比GCN+Transformer更小的车站")
    print("=" * 80)
    
    # 1. 加载FFT结果
    fft_metrics = load_fft_results()
    
    # 2. 加载主模型结果
    main_predictions, main_true_values = load_main_model_results()
    
    if main_predictions is None:
        print("无法加载主模型结果，退出程序")
        return
    
    # 3. 提取所有车站的高峰期序列
    all_station_data = extract_peak_sequences_for_all_stations(main_predictions, main_true_values)
    
    # 4. 计算所有车站的GCN+Transformer指标
    gcn_transformer_metrics = calculate_gcn_transformer_metrics(all_station_data)
    
    # 5. 比较两种方法并找出FFT优势车站
    comparison_results, fft_better_stations = compare_methods_and_find_fft_better_stations(
        fft_metrics, gcn_transformer_metrics)
    
    # 6. 输出结果
    print("\n" + "=" * 80)
    print("比较结果:")
    print("-" * 80)
    print(f"总车站数: {len(comparison_results)}")
    
    fft_rmse_better_count = len(fft_better_stations)
    fft_mae_better_count = sum(1 for r in comparison_results if r['fft_mae'] < r['gcn_mae'])
    fft_wmape_better_count = sum(1 for r in comparison_results if r['fft_wmape'] < r['gcn_wmape'])
    
    print(f"FFT的RMSE更小的车站数: {fft_rmse_better_count} ({fft_rmse_better_count/len(comparison_results)*100:.1f}%)")
    print(f"FFT的MAE更小的车站数: {fft_mae_better_count} ({fft_mae_better_count/len(comparison_results)*100:.1f}%)")
    print(f"FFT的WMAPE更小的车站数: {fft_wmape_better_count} ({fft_wmape_better_count/len(comparison_results)*100:.1f}%)")
    
    if len(fft_better_stations) > 0:
        print(f"\nFFT优势车站列表 (按RMSE改进幅度排序):")
        print("-" * 60)
        print(f"{'车站ID':<6} {'高峰时刻':<8} {'FFT_RMSE':<10} {'GCN_RMSE':<10} {'改进%':<8}")
        print("-" * 60)
        
        fft_better_stations_sorted = sorted(fft_better_stations, 
                                           key=lambda x: x['rmse_improvement_pct'], 
                                           reverse=True)
        
        for result in fft_better_stations_sorted[:10]:  # 显示前10个
            peak_time = result['peak_time']
            hour = peak_time // 4
            minute = (peak_time % 4) * 15
            
            print(f"{result['station_id']:<6} {hour:02d}:{minute:02d}     "
                  f"{result['fft_rmse']:<10.4f} {result['gcn_rmse']:<10.4f} "
                  f"{result['rmse_improvement_pct']:<8.2f}")
        
        if len(fft_better_stations) > 10:
            print(f"... 还有 {len(fft_better_stations) - 10} 个车站")
    
    # 7. 保存详细结果
    save_comparison_results(comparison_results, fft_better_stations)
    
    print("\n分析完成！")
    print("=" * 80)

if __name__ == "__main__":
    main()
