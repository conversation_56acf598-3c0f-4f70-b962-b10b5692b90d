# adaptive_weight_fusion_comparison.png图片横坐标500的详细解释

## 🎯 **问题回答**

### **为什么横坐标是500？**

横坐标显示500是因为代码中设置了可视化限制，只显示前500个数据点以便于图表的清晰展示。

## 📊 **数据提取的完整流程**

### **1. 原始数据规模**

```python
# 总数据点数：1,380个高峰时刻预测点
# 计算方式：276个车站 × 5个测试天 = 1,380个数据点
print(f"高峰时刻数据点数: {len(true_values)}")  # 输出：1380
```

### **2. 可视化限制设置**

在`plot_comparison`函数中（第291-293行）：

```python
# 只显示前500个点以便可视化
display_points = min(500, len(true_values))  # min(500, 1380) = 500
x_axis = range(display_points)               # range(500) = [0, 1, 2, ..., 499]
```

**原因**：
- 总数据有1,380个点，如果全部显示会导致图表过于密集
- 限制为500个点可以保持图表的清晰度和可读性
- 500个点足以展示预测趋势和对比效果

### **3. 数据切片过程**

```python
for i, method in enumerate(methods):
    ax = axes[i]
    
    if method in results:
        # 只取前500个数据点进行可视化
        predictions = results[method]['predictions'][:display_points]  # [:500]
        true_vals = true_values[:display_points]                       # [:500]
        
        # 绘制曲线
        ax.plot(x_axis, true_vals, 'b-', ...)      # x_axis = [0,1,2,...,499]
        ax.plot(x_axis, predictions, 'r--', ...)
```

## 🔍 **数据提取的详细过程**

### **步骤1: 高峰时刻数据提取**

```python
def extract_peak_time_data(self):
    # 存储高峰时刻数据
    peak_main_pred = []
    peak_main_true = []
    peak_fft_pred = []
    peak_fft_true = []
    
    for station_idx in range(276):           # 遍历276个车站
        peak_time = self.peak_times[station_idx]  # 获取该车站的高峰时刻
        
        for test_day in range(5):            # 遍历5个测试天
            # 1. 提取main_predict的高峰时刻数据
            # 计算高峰时刻在测试集中的索引位置
            actual_day = 20 + test_day       # 第21-25天
            day_start_index = actual_day * 72
            peak_absolute_index = day_start_index + peak_time
            test_set_start_index = 20 * 72
            peak_relative_index = peak_absolute_index - test_set_start_index
            
            # 提取对应的预测值和真实值
            peak_main_pred.append(self.main_predictions[station_idx, peak_relative_index])
            peak_main_true.append(self.main_true_values[station_idx, peak_relative_index])
            
            # 2. 提取FFT的高峰时刻数据（中心时刻）
            center_idx = 4  # 10个时间步的中心
            peak_fft_pred.append(fft_predictions[station_idx, test_day, center_idx])
            peak_fft_true.append(fft_true_values[station_idx, test_day, center_idx])
    
    # 最终得到1,380个数据点 (276 × 5 = 1,380)
    return (np.array(peak_main_pred), np.array(peak_main_true),
            np.array(peak_fft_pred), np.array(peak_fft_true))
```

### **步骤2: 数据组织结构**

```
原始数据结构：
├── Main Predict数据: (276个车站, 359个时间步) - 测试集全部数据
├── FFT数据: (276个车站, 5天, 10个时间步) - 高峰时刻序列数据
└── 高峰时刻信息: (276个车站) - 每个车站的高峰时刻索引

提取后的数据结构：
├── peak_main_pred: [1,380个数据点] - Main Predict在高峰时刻的预测值
├── peak_main_true: [1,380个数据点] - Main Predict在高峰时刻的真实值
├── peak_fft_pred:  [1,380个数据点] - FFT在高峰时刻的预测值
└── peak_fft_true:  [1,380个数据点] - FFT在高峰时刻的真实值
```

### **步骤3: 数据点的具体含义**

每个数据点代表：
```
数据点索引 = station_idx * 5 + test_day

例如：
- 索引0: 车站0第1天的高峰时刻数据
- 索引1: 车站0第2天的高峰时刻数据
- 索引5: 车站1第1天的高峰时刻数据
- 索引499: 车站99第5天的高峰时刻数据
- ...
- 索引1379: 车站275第5天的高峰时刻数据
```

## 📈 **横坐标0-500的具体含义**

### **横坐标范围解释**

| 横坐标范围 | 对应的数据 | 车站范围 | 天数范围 |
|------------|------------|----------|----------|
| 0-4 | 车站0的5天高峰数据 | 车站0 | 第1-5天 |
| 5-9 | 车站1的5天高峰数据 | 车站1 | 第1-5天 |
| 10-14 | 车站2的5天高峰数据 | 车站2 | 第1-5天 |
| ... | ... | ... | ... |
| 495-499 | 车站99的5天高峰数据 | 车站99 | 第1-5天 |

### **数据密度**
- **每5个点**: 代表同一个车站在5个测试天的高峰时刻数据
- **总共500个点**: 覆盖前100个车站的完整测试数据
- **剩余880个点**: 车站100-275的数据（在图中未显示）

## 🔧 **如果要显示全部1,380个数据点**

### **修改方法**

将第292行的代码修改为：

```python
# 修改前：只显示前500个点
display_points = min(500, len(true_values))

# 修改后：显示全部数据点
display_points = len(true_values)  # 显示全部1,380个点
```

### **修改后的效果**
- 横坐标范围：0-1379
- 数据覆盖：全部276个车站的5天高峰时刻数据
- 图表密度：更密集，但包含完整信息

## 📊 **为什么选择500个点**

### **可视化考虑**
1. **清晰度**: 500个点在图表上显示清晰，不会过于密集
2. **代表性**: 覆盖前100个车站，具有足够的代表性
3. **趋势展示**: 足以展示预测方法的对比效果和趋势
4. **计算效率**: 减少绘图计算量，提高响应速度

### **实际应用**
- **性能指标**: 基于全部1,380个数据点计算（RMSE、MAE、WMAPE）
- **可视化**: 只显示前500个点用于直观对比
- **数据完整性**: 所有数据都参与了算法评估，只是可视化时进行了截取

## 🎯 **总结**

1. **横坐标500**: 是为了可视化清晰度而设置的显示限制
2. **数据来源**: 从1,380个高峰时刻预测点中选取前500个
3. **数据含义**: 每个点代表一个车站在某一天的高峰时刻预测
4. **完整性**: 性能指标基于全部1,380个数据点计算
5. **可修改**: 可以轻松修改代码显示全部数据点

这种设计既保证了算法评估的完整性，又确保了可视化结果的清晰度和可读性。

---

**解释完成时间**: 2024年  
**算法工程师**: 008  
**文件**: adaptive_weight_fusion_comparison.png  
**数据点**: 前500个（共1,380个高峰时刻预测点）
