main_predict_improved.py 测试集详细分析报告
================================================================================

测试集基本信息:
----------------------------------------
总测试样本数: 359
测试批次数: 12
测试天数: 4.99天
车站数量: 276
总预测点数: 99084

数据来源:
----------------------------------------
测试集使用原始数据的最后5天数据
每天包含72个时间步（15分钟间隔）
测试集不与训练集和验证集重叠

性能指标计算:
----------------------------------------
✅ RMSE、MAE、WMAPE、R2均在测试集上计算
✅ 包含全部276个车站的预测结果
✅ 覆盖完整的测试时间段
✅ 使用反归一化后的真实数值进行计算

数据归一化:
----------------------------------------
最大值: 4744.00
最小值: 0.00
归一化方式: (x - min) / (max - min)
性能指标计算时已进行反归一化
