自适应权重融合算法结果分析
================================================================================

融合目标:
- 结合main_predict_improved.py和FFT算法
- 使用自适应权重融合策略
- 提高高峰期预测准确率
- 降低RMSE、MAE、WMAPE指标

评估结果:
----------------------------------------------------------------------
方法                   RMSE       MAE        WMAPE      R2        
----------------------------------------------------------------------
Main Predict         47.5219    25.6232    0.0967     0.9837    
FFT Method           37.2286    20.5881    0.0776     0.9900    
自适应权重融合              37.4049    20.7441    0.0782     0.9899    

自适应权重融合策略分析:
----------------------------------------
自适应权重融合策略性能:
  RMSE: 37.4049
  MAE:  20.7441
  WMAPE: 0.0782 (7.82%)
  R2:   0.9899

相比Main Predict改进:
  RMSE: 47.5219 → 37.4049 (-21.29%)
  MAE:  25.6232 → 20.7441 (-19.04%)
  WMAPE: 0.0967 → 0.0782 (-19.12%)

相比FFT Method对比:
  RMSE: 37.2286 vs 37.4049 (+0.47%)
  MAE:  20.5881 vs 20.7441 (+0.76%)
  WMAPE: 0.0776 vs 0.0782 (+0.76%)
