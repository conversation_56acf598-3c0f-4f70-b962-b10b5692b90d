# 车站30,14,95,97,262,33对比曲线可视化报告

## 🎯 **可视化完成说明**

我已经成功绘制了车站30,14,95,97,262,33用FFT、GCN+Transformer、FFT+GCN+Transformer三种方法预测的测试集后五天高峰期10个时间步的预测值与真实值对比曲线，横坐标范围为0-50。

## 📊 **生成的可视化文件**

### **主要输出**
- **`result/stations_30_14_95_97_262_33_comparison_curves.png`** - 对比曲线图
- **`result/stations_30_14_95_97_262_33_comparison_summary.txt`** - 性能汇总表

## 🎨 **可视化设计特点**

### **图表布局**
- **布局**: 3×2子图布局，每个车站单独显示
- **尺寸**: 20×18英寸，高分辨率(300 DPI)
- **横坐标**: 0-50范围 (5天×10时间步)

### **曲线样式**
| 方法 | 线型 | 颜色 | 标记 | 说明 |
|------|------|------|------|------|
| **真实值** | 实线(-) | 黑色 | 圆圈(o) | 基准线，粗线显示 |
| **GCN+Transformer** | 虚线(--) | 蓝色 | 方块(s) | 深度学习方法 |
| **FFT** | 点线(:) | 红色 | 三角(^) | 频域分析方法 |
| **FFT+GCN+Trans** | 点划线(-.) | 绿色 | 菱形(d) | 混合方法 |

### **信息展示**
- **标题**: 显示车站ID和高峰时刻
- **图例**: 包含各方法的WMAPE值
- **统计信息**: 显示平均客流量
- **网格**: 浅色网格便于读数

## 📈 **各车站性能对比详情**

### **车站30 (高峰时刻2:45)**
| 方法 | RMSE | MAE | WMAPE | 性能评级 |
|------|------|-----|-------|----------|
| GCN+Transformer | 37.76 | 31.58 | **6.61%** | ✅ 优秀 |
| FFT | 55.98 | 47.53 | **9.95%** | ✅ 良好 |
| **FFT+GCN+Trans** | **32.61** | **26.36** | **5.52%** | ✅ 最优 |

**观察**: 混合方法表现最佳，WMAPE仅5.52%

### **车站14 (高峰时刻12:15)**
| 方法 | RMSE | MAE | WMAPE | 性能评级 |
|------|------|-----|-------|----------|
| **GCN+Transformer** | **79.58** | **64.28** | **19.77%** | ⚠️ 一般 |
| FFT | 88.57 | 70.47 | 21.67% | ⚠️ 一般 |
| FFT+GCN+Trans | 58.06 | 47.45 | **14.59%** | ✅ 良好 |

**观察**: 午间高峰预测困难，混合方法相对最佳

### **车站95 (高峰时刻3:00)**
| 方法 | RMSE | MAE | WMAPE | 性能评级 |
|------|------|-----|-------|----------|
| **GCN+Transformer** | **61.44** | **46.70** | **3.44%** | ✅ 最优 |
| FFT | 116.92 | 85.46 | 6.29% | ✅ 优秀 |
| FFT+GCN+Trans | 55.59 | 43.34 | **3.19%** | ✅ 最优 |

**观察**: 高客流车站，混合方法略优于GCN+Transformer

### **车站97 (高峰时刻2:45)**
| 方法 | RMSE | MAE | WMAPE | 性能评级 |
|------|------|-----|-------|----------|
| **GCN+Transformer** | **29.93** | **22.86** | **5.56%** | ✅ 优秀 |
| FFT | 39.50 | 32.79 | 7.97% | ✅ 良好 |
| FFT+GCN+Trans | 22.92 | 18.09 | **4.40%** | ✅ 最优 |

**观察**: 混合方法表现最佳，各项指标均最优

### **车站262 (高峰时刻2:30)**
| 方法 | RMSE | MAE | WMAPE | 性能评级 |
|------|------|-----|-------|----------|
| **GCN+Transformer** | **20.03** | **15.26** | **6.77%** | ✅ 优秀 |
| FFT | 21.82 | 16.69 | 7.40% | ✅ 优秀 |
| FFT+GCN+Trans | 14.87 | 11.97 | **5.31%** | ✅ 最优 |

**观察**: 低客流车站，混合方法显著改善了预测精度

### **车站33 (高峰时刻2:45)**
| 方法 | RMSE | MAE | WMAPE | 性能评级 |
|------|------|-----|-------|----------|
| GCN+Transformer | 97.90 | 69.28 | 5.75% | ✅ 优秀 |
| **FFT** | **53.39** | **43.28** | **3.59%** | ✅ 最优 |
| FFT+GCN+Trans | 52.76 | 41.02 | **3.40%** | ✅ 最优 |

**观察**: 高客流车站，FFT和混合方法都表现优异

## 📊 **整体性能统计**

### **平均性能对比**
| 方法 | 平均RMSE | 平均MAE | 平均WMAPE | 排名 |
|------|----------|---------|-----------|------|
| GCN+Transformer | 54.45 | 41.73 | **8.98%** | 第2名 |
| FFT | 62.70 | 49.46 | **10.21%** | 第3名 |
| **FFT+GCN+Trans** | **39.47** | **31.37** | **7.24%** | 🥇 第1名 |

### **最佳方法统计**
| 最佳方法 | 车站数量 | 车站列表 | 优势 |
|----------|----------|----------|------|
| **FFT+GCN+Trans** | **5个** | 30, 14, 95, 97, 262 | 混合优势明显 |
| **FFT** | **1个** | 33 | 高客流车站优势 |
| **GCN+Transformer** | **0个** | - | 单独使用不是最优 |

## 🔍 **可视化观察要点**

### **1. 曲线拟合度**
- **真实值曲线**: 黑色实线，显示实际客流变化模式
- **预测曲线**: 三种颜色曲线，越接近黑线表示预测越准确
- **拟合效果**: 混合方法(绿色)通常最接近真实值

### **2. 峰值预测**
- **高峰捕捉**: 观察各方法对客流高峰的预测能力
- **谷值预测**: 观察各方法对客流低谷的预测能力
- **趋势跟踪**: 观察各方法对客流趋势变化的跟踪能力

### **3. 车站差异**
- **高客流车站** (95, 33): 平均客流>1000人，预测相对容易
- **中等客流车站** (30, 97, 14): 平均客流300-500人，预测难度中等
- **低客流车站** (262): 平均客流<300人，预测相对困难

### **4. 时间模式**
- **横坐标0-50**: 代表5天×10时间步的连续预测
- **周期性**: 观察是否存在明显的周期性模式
- **稳定性**: 观察预测的稳定性和一致性

## 🎯 **可视化价值**

### **1. 直观对比**
- 清晰展示三种方法的预测效果差异
- 便于识别各方法的优势和劣势
- 直观显示预测精度的车站差异

### **2. 决策支持**
- 为选择最佳预测方法提供可视化依据
- 为不同车站采用不同策略提供参考
- 为模型优化方向提供指导

### **3. 技术验证**
- 验证混合方法的有效性
- 展示不同方法在不同场景下的适用性
- 提供模型性能的直观评估

## 📁 **文件说明**

### **图像文件**
- **文件名**: `stations_30_14_95_97_262_33_comparison_curves.png`
- **分辨率**: 300 DPI，适合打印和展示
- **格式**: PNG，支持透明背景
- **尺寸**: 20×18英寸，清晰度高

### **数据文件**
- **文件名**: `stations_30_14_95_97_262_33_comparison_summary.txt`
- **内容**: 详细的性能指标对比表
- **格式**: 文本格式，便于进一步分析

## 🎉 **总结**

### **核心发现**
1. **混合方法表现最佳**: 在6个车站中有5个表现最优
2. **车站差异明显**: 不同车站的最佳方法可能不同
3. **可视化效果清晰**: 直观展示了三种方法的预测效果

### **实际应用价值**
- ✅ **为运营决策提供可视化支持**
- ✅ **为模型选择提供直观依据**
- ✅ **为系统优化提供改进方向**

### **技术成果**
这个可视化分析成功展示了三种预测方法在实际应用中的表现，为地铁客流预测系统的优化和应用提供了重要的技术参考。

---

**可视化完成时间**: 2024年  
**算法工程师**: 008  
**可视化对象**: 车站30,14,95,97,262,33三种方法对比  
**核心成果**: 混合方法在5/6车站表现最优，平均WMAPE 7.24%
