Transformer位置编码详细分析报告
================================================================================

1. 位置编码公式:
----------------------------------------
PE(pos, 2i)   = sin(pos / 10000^(2i/d_model))
PE(pos, 2i+1) = cos(pos / 10000^(2i/d_model))

2. 工程配置参数:
----------------------------------------
d_model: 64 (模型维度)
seq_len: 3 (序列长度)
位置含义: 0=Week, 1=Day, 2=Hour

3. 具体位置编码值:
----------------------------------------

位置 0 (Week):
前16个维度的编码值:
维度  0- 3: [0. 1. 0. 1.]
维度  4- 7: [0. 1. 0. 1.]
维度  8-11: [0. 1. 0. 1.]
维度 12-15: [0. 1. 0. 1.]

位置 1 (Day):
前16个维度的编码值:
维度  0- 3: [0.84147096 0.54030234 0.68156135 0.731761  ]
维度  4- 7: [0.53316844 0.8460091  0.40930894 0.91239583]
维度  8-11: [0.3109836  0.9504153  0.23492105 0.9720145 ]
维度 12-15: [0.17689219 0.9842302  0.13295726 0.99112177]

位置 2 (Hour):
前16个维度的编码值:
维度  0- 3: [ 0.9092974  -0.41614684  0.99748     0.07094827]
维度  4- 7: [0.9021307  0.43146282 0.74690354 0.66493237]
维度  8-11: [0.5911271  0.8065784  0.45669332 0.8896242 ]
维度 12-15: [0.34820527 0.93741834 0.26355368 0.96464473]

4. 位置编码特点分析:
----------------------------------------
- 位置0 (Week): 所有维度都是0或1，因为sin(0)=0, cos(0)=1
- 位置1 (Day): 根据不同频率产生不同的sin/cos值
- 位置2 (Hour): 频率更高，产生更复杂的编码模式
- 偶数维度使用sin函数，奇数维度使用cos函数
- 不同维度有不同的频率，形成独特的位置标识

5. 在地铁预测中的作用:
----------------------------------------
- 帮助Transformer区分周期性、日期性、时刻性特征
- 为每个时间维度提供唯一的位置标识
- 通过加法方式与输入特征融合
- 保持模型对时间顺序的感知能力
