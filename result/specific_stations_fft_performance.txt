指定车站FFT高峰期10个时间步预测性能分析
================================================================================

分析说明:
----------------------------------------
- 分析车站: [30, 14, 95, 97, 262, 33]
- 基于fft_continuous_prediction.py方法
- 数据来源: FFT模型的预测结果
- 评估范围: 测试集(最后5天)的高峰时刻左右10个时间步
- 每个车站: 10个时间步 × 5天 = 50个预测值
- 总计: 6个车站 × 50 = 300个预测值

整体评估指标(指定车站FFT高峰时刻序列):
----------------------------------------
总RMSE: 70.4284
总MAE: 53.3890
总WMAPE: 0.0800 (8.00%)
总R2: 0.9807

各车站详细指标:
--------------------------------------------------------------------------------
车站ID | 高峰时刻 | RMSE     | MAE      | WMAPE    | R2       | 数据点数 | 预测均值 | 真实均值
--------------------------------------------------------------------------------
    30 | 02:45     |  38.1661 |  30.9754 |   0.0649 |   0.9140 |       50 |   474.60 |   477.64
    14 | 12:15     |  65.6876 |  52.9457 |   0.1628 |   0.6129 |       50 |   339.79 |   325.18
    95 | 03:00     | 118.9775 |  92.1239 |   0.0678 |   0.9165 |       50 |  1307.02 |  1358.64
    97 | 02:45     |  53.1196 |  43.0100 |   0.1045 |   0.8202 |       50 |   445.20 |   411.46
   262 | 02:30     |  56.3048 |  48.7987 |   0.2163 |   0.2549 |       50 |   273.00 |   225.56
    33 | 02:45     |  61.9832 |  52.4803 |   0.0435 |   0.9724 |       50 |  1186.89 |  1205.62

数据统计:
----------------------------------------
总预测值范围: [116.02, 1996.53]
总真实值范围: [103.00, 1918.00]
总预测值平均: 671.08
总真实值平均: 667.35
