指定车站高峰期10个时间步预测性能分析
================================================================================

分析说明:
----------------------------------------
- 分析车站: [30, 14, 95, 97, 262, 33]
- 基于analyze_main_predict_on_fft_peak_sequences.py方法
- 数据来源: main_predict_improved.py的预测结果
- 评估范围: 测试集(最后5天)的高峰时刻左右10个时间步
- 每个车站: 10个时间步 × 5天 = 50个预测值
- 总计: 6个车站 × 50 = 300个预测值

整体评估指标(指定车站高峰时刻序列):
----------------------------------------
总RMSE: 61.1222
总MAE: 41.6600
总WMAPE: 0.0624 (6.24%)
总R2: 0.9854

各车站详细指标:
--------------------------------------------------------------------------------
车站ID | RMSE     | MAE      | WMAPE    | R2       | 数据点数 | 预测均值 | 真实均值
--------------------------------------------------------------------------------
    30 |  37.7563 |  31.5800 |   0.0661 |   0.9159 |       50 |   453.86 |   477.64
    14 |  79.5784 |  64.2800 |   0.1977 |   0.4318 |       50 |   309.54 |   325.18
    95 |  61.4425 |  46.7000 |   0.0344 |   0.9777 |       50 |  1353.14 |  1358.64
    97 |  29.9276 |  22.8600 |   0.0556 |   0.9429 |       50 |   412.12 |   411.46
   262 |  20.0275 |  15.2600 |   0.0677 |   0.9057 |       50 |   216.70 |   225.56
    33 |  97.9046 |  69.2800 |   0.0575 |   0.9312 |       50 |  1154.78 |  1205.62

数据统计:
----------------------------------------
总预测值范围: [116.00, 1930.00]
总真实值范围: [103.00, 1918.00]
总预测值平均: 650.02
总真实值平均: 667.35
