# 车站30,14,95,97,262,33高峰期预测性能总结

## 🎯 **基于analyze_main_predict_on_fft_peak_sequences.py方法的分析结果**

根据你的要求，我已经利用`analyze_main_predict_on_fft_peak_sequences.py`文件的方法，统计了车站30,14,95,97,262,33在数据集后五天高峰10个时间步的预测性能。

## 📊 **总体性能指标**

### **6个车站的总RMSE、MAE、WMAPE值**

| 指标 | 数值 | 说明 |
|------|------|------|
| **总RMSE** | **61.1222** | 均方根误差 |
| **总MAE** | **41.6600** | 平均绝对误差 |
| **总WMAPE** | **0.0624 (6.24%)** | 加权平均绝对百分比误差 |
| **总R²** | **0.9854** | 决定系数 |
| **总数据点** | **300个** | 6车站×5天×10时间步 |

## 📈 **各车站详细性能指标**

### **完整的车站级别分析**

| 车站ID | RMSE | MAE | WMAPE | R² | 高峰时刻 | 预测均值 | 真实均值 |
|--------|------|-----|-------|----|---------|---------|---------| 
| **30** | 37.76 | 31.58 | **6.61%** | 0.9159 | 2:45 | 453.86 | 477.64 |
| **14** | 79.58 | 64.28 | **19.77%** | 0.4318 | 12:15 | 309.54 | 325.18 |
| **95** | 61.44 | 46.70 | **3.44%** | 0.9777 | 3:00 | 1353.14 | 1358.64 |
| **97** | 29.93 | 22.86 | **5.56%** | 0.9429 | 2:45 | 412.12 | 411.46 |
| **262** | 20.03 | 15.26 | **6.77%** | 0.9057 | 2:30 | 216.70 | 225.56 |
| **33** | 97.90 | 69.28 | **5.75%** | 0.9312 | 2:45 | 1154.78 | 1205.62 |

## 🔍 **关键发现**

### **1. 整体性能优秀**
- ✅ **总WMAPE仅6.24%**: 达到优秀级别的预测精度
- ✅ **总R²高达0.9854**: 模型拟合度极高
- ✅ **300个数据点**: 完整覆盖6个车站×5天×10时间步

### **2. 车站性能差异明显**

#### **最佳表现车站**:
1. **车站95**: WMAPE 3.44% (最优)
   - R² = 0.9777 (拟合度最高)
   - 高客流车站 (平均1353人)
   - 高峰时刻: 3:00

2. **车站97**: WMAPE 5.56% (次优)
   - R² = 0.9429 (拟合度良好)
   - 中等客流车站 (平均412人)
   - 高峰时刻: 2:45

3. **车站33**: WMAPE 5.75% (第三)
   - R² = 0.9312 (拟合度良好)
   - 高客流车站 (平均1155人)
   - 高峰时刻: 2:45

#### **表现较差车站**:
1. **车站14**: WMAPE 19.77% (最差)
   - R² = 0.4318 (拟合度较低)
   - 中等客流车站 (平均309人)
   - 高峰时刻: 12:15 (午间高峰)

### **3. 高峰时刻分布**

| 高峰时刻 | 车站数量 | 车站列表 | 特点 |
|----------|----------|----------|------|
| **2:30** | 1个 | 262 | 早晨高峰前 |
| **2:45** | 3个 | 30, 97, 33 | 早晨高峰前 |
| **3:00** | 1个 | 95 | 早晨高峰前 |
| **12:15** | 1个 | 14 | 午间高峰 |

**观察**: 大部分车站(5/6)的高峰期集中在早晨2:30-3:00，只有车站14是午间高峰(12:15)

## 📊 **性能分析**

### **客流量与预测精度的关系**

| 客流量级别 | 车站 | 平均客流 | WMAPE | 观察 |
|------------|------|----------|-------|------|
| **高客流** | 95, 33 | >1000人 | 3.44%, 5.75% | 预测精度高 |
| **中等客流** | 30, 97, 14 | 300-500人 | 6.61%, 5.56%, 19.77% | 精度差异大 |
| **低客流** | 262 | <250人 | 6.77% | 精度中等 |

**发现**: 高客流车站(95, 33)预测精度普遍较高，可能因为数据模式更稳定。

### **高峰时刻与预测精度的关系**

| 高峰时刻类型 | 车站 | WMAPE | 观察 |
|-------------|------|-------|------|
| **早晨高峰前** | 30, 95, 97, 262, 33 | 3.44%-6.77% | 预测精度高 |
| **午间高峰** | 14 | 19.77% | 预测精度较差 |

**发现**: 早晨高峰前的预测精度明显优于午间高峰，可能因为早晨客流模式更规律。

## 🎯 **技术分析**

### **数据来源和方法**
- **基础方法**: `analyze_main_predict_on_fft_peak_sequences.py`
- **数据来源**: `main_predict_improved.py`的预测结果
- **测试集**: 数据集后5天 (第21-25天)
- **高峰定义**: 基于FFT分析确定的各车站高峰时刻
- **序列长度**: 高峰时刻前后10个时间步

### **评估框架**
- **时间映射**: 将FFT高峰时刻映射到main_predict测试集
- **序列提取**: 每个车站每天提取10个高峰时间步
- **指标计算**: 基于300个数据点计算总体和分车站指标

## 📈 **数据统计**

### **预测值与真实值对比**
- **总预测值范围**: [116.00, 1930.00]
- **总真实值范围**: [103.00, 1918.00]
- **总预测值平均**: 650.02人
- **总真实值平均**: 667.35人
- **平均偏差**: -17.33人 (略微低估)

### **各车站预测偏差**
| 车站ID | 预测均值 | 真实均值 | 偏差 | 偏差率 |
|--------|----------|----------|------|--------|
| 30 | 453.86 | 477.64 | -23.78 | -4.98% |
| 14 | 309.54 | 325.18 | -15.64 | -4.81% |
| 95 | 1353.14 | 1358.64 | -5.50 | -0.40% |
| 97 | 412.12 | 411.46 | +0.66 | +0.16% |
| 262 | 216.70 | 225.56 | -8.86 | -3.93% |
| 33 | 1154.78 | 1205.62 | -50.84 | -4.22% |

**观察**: 大部分车站存在轻微低估，车站97预测最准确(偏差仅0.16%)。

## 🎉 **总结**

### **核心结论**
1. **总体性能优秀**: 6个车站的总WMAPE为6.24%，达到优秀级别
2. **车站差异明显**: 最佳车站95 (WMAPE 3.44%) vs 最差车站14 (WMAPE 19.77%)
3. **早晨高峰预测更准**: 早晨高峰前车站预测精度普遍优于午间高峰
4. **高客流车站表现好**: 高客流车站的预测精度通常更高

### **实际应用价值**
- ✅ **可用于运营决策**: 6.24%的总体WMAPE满足实际应用需求
- ✅ **车站差异化管理**: 可针对不同车站采用差异化预测策略
- ✅ **时段优化**: 早晨高峰前预测更可靠，可重点应用

### **改进建议**
- 🔧 **针对车站14优化**: 午间高峰预测需要特别关注
- 📊 **客流分级管理**: 根据客流量级别采用不同预测策略
- ⏰ **时段特化**: 考虑为不同时段开发专门的预测模型

---

**分析完成时间**: 2024年  
**算法工程师**: 008  
**分析方法**: analyze_main_predict_on_fft_peak_sequences.py  
**核心发现**: 6个车站总WMAPE 6.24%，预测性能优秀
