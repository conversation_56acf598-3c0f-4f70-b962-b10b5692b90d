混合预测集成算法结果分析
================================================================================

集成目标:
- 结合main_predict_improved.py和FFT算法
- 提高高峰期预测准确率
- 降低RMSE、MAE、WMAPE指标

评估结果:
----------------------------------------------------------------------
方法              RMSE       MAE        WMAPE      R2         改进        
----------------------------------------------------------------------
选择性最优           21.0870    13.7853    0.0121     0.9994     82.64     %
FFT Method      23.7511    15.5862    0.0137     0.9992     80.37     %
自适应权重           27.5845    18.3139    0.0161     0.9990     76.94     %
中位数融合           61.8101    40.6601    0.0357     0.9948     48.80     %
置信度融合           66.8340    42.7136    0.0375     0.9939     46.21     %
加权平均(7:3)       85.1581    55.8763    0.0491     0.9901     29.64     %
Main Predict    121.1609   79.4094    0.0697     0.9799     0.00      %

最佳策略分析:
----------------------------------------
最佳集成策略: 选择性最优
性能改进:
  RMSE: 121.1609 → 21.0870 (-82.60%)
  MAE:  79.4094 → 13.7853 (-82.64%)
  WMAPE: 0.0697 → 0.0121 (-82.64%)
