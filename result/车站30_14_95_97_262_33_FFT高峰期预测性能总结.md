# 车站30,14,95,97,262,33 FFT高峰期预测性能总结

## 🎯 **基于fft_continuous_prediction.py方法的分析结果**

根据你的要求，我已经利用`fft_continuous_prediction.py`文件的方法，统计了车站30,14,95,97,262,33在数据集后五天高峰10个时间步的预测性能。

## 📊 **总体性能指标**

### **6个车站的总RMSE、MAE、WMAPE值**

| 指标 | 数值 | 说明 |
|------|------|------|
| **总RMSE** | **70.4284** | 均方根误差 |
| **总MAE** | **53.3890** | 平均绝对误差 |
| **总WMAPE** | **0.0800 (8.00%)** | 加权平均绝对百分比误差 |
| **总R²** | **0.9807** | 决定系数 |
| **总数据点** | **300个** | 6车站×5天×10时间步 |

## 📈 **各车站详细性能指标**

### **完整的车站级别分析**

| 车站ID | RMSE | MAE | WMAPE | R² | 高峰时刻 | 预测均值 | 真实均值 | 性能评级 |
|--------|------|-----|-------|----|---------|---------|---------|---------| 
| **30** | 38.17 | 30.98 | **6.49%** | 0.9140 | 2:45 | 474.60 | 477.64 | ✅ 优秀 |
| **14** | 65.69 | 52.95 | **16.28%** | 0.6129 | 12:15 | 339.79 | 325.18 | ⚠️ 一般 |
| **95** | 118.98 | 92.12 | **6.78%** | 0.9165 | 3:00 | 1307.02 | 1358.64 | ✅ 优秀 |
| **97** | 53.12 | 43.01 | **10.45%** | 0.8202 | 2:45 | 445.20 | 411.46 | ✅ 良好 |
| **262** | 56.30 | 48.80 | **21.63%** | 0.2549 | 2:30 | 273.00 | 225.56 | ⚠️ 一般 |
| **33** | 61.98 | 52.48 | **4.35%** | 0.9724 | 2:45 | 1186.89 | 1205.62 | ✅ 优秀 |

## 🔍 **关键发现**

### **1. 整体性能良好**
- ✅ **总WMAPE为8.00%**: 达到良好级别的预测精度
- ✅ **总R²高达0.9807**: 模型拟合度极高
- ✅ **300个数据点**: 完整覆盖6个车站×5天×10时间步

### **2. 车站性能差异明显**

#### **最佳表现车站**:
1. **车站33**: WMAPE 4.35% (最优)
   - R² = 0.9724 (拟合度最高)
   - 高客流车站 (平均1187人)
   - 高峰时刻: 2:45

2. **车站30**: WMAPE 6.49% (次优)
   - R² = 0.9140 (拟合度良好)
   - 中等客流车站 (平均475人)
   - 高峰时刻: 2:45

3. **车站95**: WMAPE 6.78% (第三)
   - R² = 0.9165 (拟合度良好)
   - 高客流车站 (平均1307人)
   - 高峰时刻: 3:00

#### **表现较差车站**:
1. **车站262**: WMAPE 21.63% (最差)
   - R² = 0.2549 (拟合度较低)
   - 低客流车站 (平均273人)
   - 高峰时刻: 2:30

2. **车站14**: WMAPE 16.28% (次差)
   - R² = 0.6129 (拟合度中等)
   - 中等客流车站 (平均340人)
   - 高峰时刻: 12:15 (午间高峰)

### **3. 高峰时刻分布**

| 高峰时刻 | 车站数量 | 车站列表 | 平均WMAPE | 特点 |
|----------|----------|----------|-----------|------|
| **2:30** | 1个 | 262 | 21.63% | 早晨高峰前，表现较差 |
| **2:45** | 3个 | 30, 97, 33 | 7.10% | 早晨高峰前，表现优秀 |
| **3:00** | 1个 | 95 | 6.78% | 早晨高峰前，表现优秀 |
| **12:15** | 1个 | 14 | 16.28% | 午间高峰，表现一般 |

**观察**: 大部分车站(5/6)的高峰期集中在早晨2:30-3:00，其中2:45时刻的车站表现最佳。

## 📊 **与GCN+Transformer方法对比**

### **同样6个车站的性能对比**

| 车站ID | FFT WMAPE | GCN+Trans WMAPE | 差异 | 最优方法 |
|--------|-----------|-----------------|------|----------|
| **30** | **6.49%** | 6.61% | FFT优0.12% | FFT |
| **14** | **16.28%** | 19.77% | FFT优3.49% | FFT |
| **95** | **6.78%** | 3.44% | GCN+Trans优3.34% | GCN+Trans |
| **97** | **10.45%** | 5.56% | GCN+Trans优4.89% | GCN+Trans |
| **262** | **21.63%** | 6.77% | GCN+Trans优14.86% | GCN+Trans |
| **33** | **4.35%** | 5.75% | FFT优1.40% | FFT |

#### **对比总结**:
- **FFT总WMAPE**: 8.00%
- **GCN+Transformer总WMAPE**: 6.24%
- **GCN+Transformer整体优于FFT**: 优势1.76%

## 🔍 **技术分析**

### **FFT方法的优势**
1. **高客流车站表现好**: 车站33、95等高客流车站预测精度高
2. **早晨高峰适应性强**: 2:45时刻的车站表现优异
3. **计算效率高**: 相比GCN+Transformer计算更简单

### **FFT方法的局限**
1. **低客流车站表现差**: 车站262等低客流车站预测精度较低
2. **午间高峰适应性差**: 车站14的午间高峰预测困难
3. **整体精度略低**: 相比GCN+Transformer有1.76%的差距

### **客流量与预测精度的关系**

| 客流量级别 | 车站 | 平均客流 | FFT WMAPE | 观察 |
|------------|------|----------|-----------|------|
| **高客流** | 95, 33 | >1000人 | 5.57% | FFT表现优秀 |
| **中等客流** | 30, 97, 14 | 300-500人 | 11.07% | FFT表现中等 |
| **低客流** | 262 | <300人 | 21.63% | FFT表现较差 |

**发现**: FFT方法在高客流车站表现优秀，但在低客流车站表现较差。

### **高峰时刻与预测精度的关系**

| 高峰时刻类型 | 车站 | FFT WMAPE | 观察 |
|-------------|------|-----------|------|
| **早晨高峰前** | 30, 95, 97, 262, 33 | 10.18% | 表现中等 |
| **午间高峰** | 14 | 16.28% | 表现较差 |

**发现**: FFT在早晨高峰前的预测精度优于午间高峰，但差异不如GCN+Transformer明显。

## 🎯 **数据来源和方法说明**

### **技术实现**
- **基础方法**: `fft_continuous_prediction.py`
- **FFT特征**: 幅度谱(5维) + 相位谱(5维) = 10维特征
- **MLP结构**: 10→64→32→10 (3层神经网络)
- **训练数据**: 前16天 (4416个序列)
- **验证数据**: 第17-20天 (1104个序列)
- **测试数据**: 最后5天 (1380个序列)

### **评估框架**
- **高峰时刻**: 基于FFT分析确定的各车站高峰时刻
- **序列长度**: 高峰时刻前后10个时间步
- **数据归一化**: MinMaxScaler归一化
- **训练轮数**: 200个epoch

## 📈 **数据统计**

### **预测值与真实值对比**
- **总预测值范围**: [116.02, 1996.53]
- **总真实值范围**: [103.00, 1918.00]
- **总预测值平均**: 671.08人
- **总真实值平均**: 667.35人
- **平均偏差**: +3.73人 (轻微高估)

### **各车站预测偏差**
| 车站ID | 预测均值 | 真实均值 | 偏差 | 偏差率 |
|--------|----------|----------|------|--------|
| 30 | 474.60 | 477.64 | -3.04 | -0.64% |
| 14 | 339.79 | 325.18 | +14.61 | +4.49% |
| 95 | 1307.02 | 1358.64 | -51.62 | -3.80% |
| 97 | 445.20 | 411.46 | +33.74 | +8.20% |
| 262 | 273.00 | 225.56 | +47.44 | +21.04% |
| 33 | 1186.89 | 1205.62 | -18.73 | -1.55% |

**观察**: 车站262存在明显高估(21.04%)，其他车站偏差相对较小。

## 🎉 **总结**

### **核心结论**
1. **总体性能良好**: 6个车站的总WMAPE为8.00%，达到良好级别
2. **车站差异显著**: 最佳车站33 (WMAPE 4.35%) vs 最差车站262 (WMAPE 21.63%)
3. **高客流车站优势**: 高客流车站的FFT预测精度通常更高
4. **相比GCN+Transformer略逊**: 整体WMAPE高1.76%

### **实际应用价值**
- ✅ **可用于运营决策**: 8.00%的总体WMAPE满足一般应用需求
- ✅ **计算效率高**: FFT+MLP的计算复杂度远低于GCN+Transformer
- ✅ **高客流车站可靠**: 对于高客流车站预测精度较高

### **改进建议**
- 🔧 **针对低客流车站优化**: 车站262等低客流车站需要特别关注
- 📊 **客流分级处理**: 根据客流量级别采用不同的FFT参数
- ⏰ **时段特化**: 考虑为午间高峰开发专门的FFT模型
- 🔗 **混合策略**: 在低客流车站使用GCN+Transformer，高客流车站使用FFT

---

**分析完成时间**: 2024年  
**算法工程师**: 008  
**分析方法**: fft_continuous_prediction.py  
**核心发现**: 6个车站总WMAPE 8.00%，FFT方法在高客流车站表现优秀
