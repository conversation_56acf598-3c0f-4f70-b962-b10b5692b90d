"""
测试融合模型相关的所有导入
"""
import sys
import os

# 添加当前目录到Python路径
if '.' not in sys.path:
    sys.path.append('.')

def test_all_imports():
    """测试所有必要的导入"""
    print("Testing all imports for GCN+FFT Fusion system...")
    
    try:
        # 基础模块
        print("✓ Testing basic modules...")
        import numpy as np
        import torch
        from torch import nn
        print("  ✓ Basic modules imported successfully")
        
        # 可选模块
        print("✓ Testing optional modules...")
        try:
            from torch.utils.tensorboard import SummaryWriter
            print("  ✓ TensorBoard available")
        except ImportError:
            print("  ⚠ TensorBoard not available (optional)")
        
        # 工具模块
        print("✓ Testing utility modules...")
        from utils.utils import GetLaplacian
        from utils.earlystopping import EarlyStopping
        print("  ✓ Utility modules imported successfully")
        
        # 数据模块
        print("✓ Testing data modules...")
        from data.get_inflow_only_dataloader import get_inflow_only_dataloader
        from data.datasets import Traffic_inflow
        print("  ✓ Data modules imported successfully")
        
        # 模型模块
        print("✓ Testing model modules...")
        from model.gcn_fft_fusion_model import GCN_FFT_FusionModel
        from model.GCN_layers import GraphConvolution
        print("  ✓ Model modules imported successfully")
        
        print("\n🎉 All imports successful!")
        return True
        
    except Exception as e:
        print(f"\n❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_script_import():
    """测试主脚本导入"""
    print("\nTesting main script imports...")
    
    try:
        # 测试主训练脚本导入
        print("✓ Testing main_gcn_fft_fusion.py...")
        import main_gcn_fft_fusion
        print("  ✓ main_gcn_fft_fusion imported successfully")
        
        # 测试预测脚本导入
        print("✓ Testing predict_gcn_fft_fusion.py...")
        import predict_gcn_fft_fusion
        print("  ✓ predict_gcn_fft_fusion imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Main script import failed: {e}")
        return False

def test_model_creation():
    """测试模型创建"""
    print("\nTesting model creation...")

    try:
        import torch
        from model.gcn_fft_fusion_model import GCN_FFT_FusionModel

        device = torch.device('cpu')
        model = GCN_FFT_FusionModel(
            time_lag=10, 
            pre_len=1, 
            station_num=276, 
            device=device, 
            peak_time_steps=10
        )
        
        print(f"  ✓ Model created successfully")
        print(f"  ✓ Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model creation failed: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("GCN+FFT Fusion Import Test")
    print("=" * 60)
    
    tests = [
        ("All Imports", test_all_imports),
        ("Main Script Import", test_main_script_import),
        ("Model Creation", test_model_creation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 40)
        result = test_func()
        results.append((test_name, result))
    
    # 总结
    print("\n" + "=" * 60)
    print("Test Results Summary:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"  {test_name:<20}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All import tests passed!")
        print("Your GCN+FFT Fusion system is ready to use.")
        print("\nNext steps:")
        print("  1. Run 'python main_gcn_fft_fusion.py' to train the model")
        print("  2. Run 'python predict_gcn_fft_fusion.py' to make predictions")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed.")
        print("Please fix the import issues before proceeding.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
