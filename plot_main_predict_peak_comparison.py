#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
绘制main_predict_improved.py模型在指定车站的高峰时刻预测对比图
只显示测试集5天每天10个高峰时刻的预测值与真实值对比

Author: 008 - AI Algorithm Engineer for Subway Flow Prediction
"""

import numpy as np
import matplotlib.pyplot as plt
import os

def load_main_predict_results():
    """加载main_predict的预测结果"""
    print("正在加载main_predict的预测结果...")
    
    # 加载预测值 (276个车站, N个时间步)
    predictions = np.loadtxt('result/improved_ALL_276_stations_predictions.txt')
    print(f"预测值形状: {predictions.shape}")
    
    # 加载真实值 (276个车站, N个时间步)
    true_values = np.loadtxt('result/improved_ALL_276_stations_original.txt')
    print(f"真实值形状: {true_values.shape}")
    
    return predictions, true_values

def load_fft_peak_times():
    """加载FFT高峰时刻信息"""
    print("正在加载FFT高峰时刻信息...")
    
    # 加载高峰时刻信息
    peak_times = np.loadtxt('./data/continuous_peak_flow_dataset_peak_times.csv', delimiter=',', dtype=int)
    print(f"高峰时刻数据形状: {peak_times.shape}")
    
    return peak_times

def extract_peak_sequences_for_stations(predictions, true_values, peak_times, target_stations):
    """提取指定车站的高峰时刻序列数据"""
    print("正在提取指定车站的高峰时刻序列数据...")
    
    # 数据集参数
    train_val_days = 20  # 前20天为训练+验证集
    test_days = 5  # 最后5天为测试集
    time_steps_per_day = 72
    sequence_length = 10  # 高峰时刻左右10个时间步
    
    station_data = {}
    
    for station_idx in target_stations:
        peak_time = peak_times[station_idx]
        
        station_predictions = []
        station_true_values = []
        
        # 对测试集的每一天，提取高峰时刻序列
        for test_day in range(test_days):
            actual_day = train_val_days + test_day  # 第21-25天
            
            # 计算该天该车站高峰时刻在原始数据中的绝对索引
            day_start_index = actual_day * time_steps_per_day
            peak_center_index = day_start_index + peak_time
            
            # 计算高峰时刻左右10个时间步的索引范围
            sequence_start = peak_center_index - 4
            sequence_end = peak_center_index + 6  # 不包含end，所以是+6
            
            # 确保序列在有效范围内
            sequence_start = max(sequence_start, day_start_index)
            sequence_end = min(sequence_end, day_start_index + time_steps_per_day)
            
            # 转换为main_predict测试集中的相对索引
            test_set_start_index = train_val_days * time_steps_per_day
            
            day_predictions = []
            day_true_values = []
            
            for abs_idx in range(sequence_start, sequence_end):
                relative_idx = abs_idx - test_set_start_index
                if 0 <= relative_idx < predictions.shape[1]:
                    day_predictions.append(predictions[station_idx, relative_idx])
                    day_true_values.append(true_values[station_idx, relative_idx])
            
            # 确保每天都有10个数据点
            while len(day_predictions) < sequence_length:
                if len(day_predictions) > 0:
                    day_predictions.append(day_predictions[-1])
                    day_true_values.append(day_true_values[-1])
                else:
                    day_predictions.append(0)
                    day_true_values.append(0)
            
            # 截取到10个数据点
            day_predictions = day_predictions[:sequence_length]
            day_true_values = day_true_values[:sequence_length]
            
            station_predictions.extend(day_predictions)
            station_true_values.extend(day_true_values)
        
        station_data[station_idx] = {
            'predictions': np.array(station_predictions),
            'true_values': np.array(station_true_values),
            'peak_time': peak_time
        }
        
        print(f"车站{station_idx}: 高峰时刻{peak_time//4:02d}:{(peak_time%4)*15:02d}, 数据点数: {len(station_predictions)}")
    
    return station_data

def plot_station_comparisons(station_data, target_stations):
    """绘制车站预测对比图"""
    print("正在绘制车站预测对比图...")
    
    # 设置中文字体以避免乱码
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建图表
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Main Predict Model - Peak Time Sequences Comparison\n(Test Set: 5 Days × 10 Time Steps per Day)', 
                 fontsize=16, fontweight='bold')
    
    # 展平axes数组以便索引
    axes = axes.flatten()
    
    for i, station_idx in enumerate(target_stations):
        ax = axes[i]
        data = station_data[station_idx]
        
        predictions = data['predictions']
        true_values = data['true_values']
        peak_time = data['peak_time']
        
        # 创建x轴：50个时间步 (5天 × 10步/天)
        x_axis = np.arange(len(predictions))
        
        # 绘制预测值和真实值
        ax.plot(x_axis, predictions, 'r-', linewidth=2, alpha=0.8, label='Prediction', marker='o', markersize=3)
        ax.plot(x_axis, true_values, 'b-', linewidth=2, alpha=0.8, label='Actual', marker='s', markersize=3)
        
        # 添加每天的分界线
        for day in range(1, 5):  # 在第10, 20, 30, 40位置添加分界线
            ax.axvline(x=day*10, color='gray', linestyle='--', alpha=0.5, linewidth=1)
        
        # 设置标题和标签
        hour = peak_time // 4
        minute = (peak_time % 4) * 15
       # ax.set_title(f'Station {station_idx} (Peak: {hour:02d}:{minute:02d})', fontsize=12, fontweight='bold')
        ax.set_title(f'Station {station_idx}', fontsize=12, fontweight='bold')
        ax.set_xlabel('Time Steps (5 Days × 10 Steps)', fontsize=10)
        ax.set_ylabel('Passenger Flow', fontsize=10)
        ax.legend(fontsize=10)
        ax.grid(True, alpha=0.3)
        
        # 计算并显示性能指标
        rmse = np.sqrt(np.mean((predictions - true_values) ** 2))
        mae = np.mean(np.abs(predictions - true_values))
        wmape = np.sum(np.abs(predictions - true_values)) / np.sum(true_values) if np.sum(true_values) > 0 else 0
        
        # 在图上添加性能指标
      #  metrics_text = f'RMSE: {rmse:.1f}\nMAE: {mae:.1f}\nWMAPE: {wmape:.3f}'
      #  ax.text(0.02, 0.98, metrics_text, transform=ax.transAxes, fontsize=9,
       #         verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        # 添加天数标签
        for day in range(5):
            day_center = day * 10 + 5
            ax.text(day_center, ax.get_ylim()[1] * 0.9, f'Day {day+1}', 
                   ha='center', fontsize=8, 
                   bbox=dict(boxstyle='round,pad=0.2', facecolor='lightblue', alpha=0.7))
    
    plt.tight_layout()
    
    # 保存图片
    if not os.path.exists('result'):
        os.makedirs('result')
    
    plt.savefig('result/main_predict_peak_sequences_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("预测对比图已保存到: result/main_predict_peak_sequences_comparison.png")

def create_detailed_comparison_table(station_data, target_stations):
    """创建详细的对比数据表"""
    print("正在创建详细对比数据表...")
    
    if not os.path.exists('result'):
        os.makedirs('result')
    
    with open('result/main_predict_peak_sequences_detailed_data.txt', 'w', encoding='utf-8') as f:
        f.write("main_predict模型指定车站高峰时刻序列详细数据\n")
        f.write("=" * 80 + "\n\n")
        
        f.write("数据说明:\n")
        f.write("- 测试集: 5天数据\n")
        f.write("- 每天: 10个高峰时刻序列数据点\n")
        f.write("- 总计: 每个车站50个数据点\n\n")
        
        for station_idx in target_stations:
            data = station_data[station_idx]
            predictions = data['predictions']
            true_values = data['true_values']
            peak_time = data['peak_time']
            
            hour = peak_time // 4
            minute = (peak_time % 4) * 15
            
            f.write(f"车站 {station_idx} (高峰时刻: {hour:02d}:{minute:02d})\n")
            f.write("-" * 60 + "\n")
            
            # 计算性能指标
            rmse = np.sqrt(np.mean((predictions - true_values) ** 2))
            mae = np.mean(np.abs(predictions - true_values))
            wmape = np.sum(np.abs(predictions - true_values)) / np.sum(true_values) if np.sum(true_values) > 0 else 0
            
            f.write(f"性能指标: RMSE={rmse:.2f}, MAE={mae:.2f}, WMAPE={wmape:.4f}\n\n")
            
            # 按天显示数据
            for day in range(5):
                start_idx = day * 10
                end_idx = start_idx + 10
                
                day_pred = predictions[start_idx:end_idx]
                day_true = true_values[start_idx:end_idx]
                
                f.write(f"第{day+1}天数据:\n")
                f.write("时间步 | 预测值  | 真实值  | 误差\n")
                f.write("-" * 35 + "\n")
                
                for i in range(10):
                    error = abs(day_pred[i] - day_true[i])
                    f.write(f"{i+1:6d} | {day_pred[i]:7.1f} | {day_true[i]:7.1f} | {error:6.1f}\n")
                
                f.write("\n")
            
            f.write("\n" + "=" * 60 + "\n\n")
    
    print("详细数据表已保存到: result/main_predict_peak_sequences_detailed_data.txt")

def main():
    """主函数"""
    print("=" * 80)
    print("main_predict模型指定车站高峰时刻序列预测对比图生成")
    print("=" * 80)
    
    # 指定要分析的车站
    target_stations = [4, 18, 30, 60, 94, 232]
    print(f"目标车站: {target_stations}")
    
    # 1. 加载main_predict的预测结果
    predictions, true_values = load_main_predict_results()
    
    # 2. 加载FFT高峰时刻信息
    peak_times = load_fft_peak_times()
    
    # 3. 提取指定车站的高峰时刻序列数据
    station_data = extract_peak_sequences_for_stations(predictions, true_values, peak_times, target_stations)
    
    # 4. 绘制对比图
    plot_station_comparisons(station_data, target_stations)
    
    # 5. 创建详细数据表
    create_detailed_comparison_table(station_data, target_stations)
    
    print("\n" + "=" * 80)
    print("对比图生成完成!")
    print("生成文件:")
    print("- result/main_predict_peak_sequences_comparison.png (对比图)")
    print("- result/main_predict_peak_sequences_detailed_data.txt (详细数据)")
    print("=" * 80)

if __name__ == "__main__":
    main()
