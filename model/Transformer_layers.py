import torch
import torch.nn as nn
import math


class PositionalEncoding(nn.Module):
    """位置编码模块，为序列中的每个位置添加位置信息"""
    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        self.register_buffer('pe', pe)

    def forward(self, x):
        # x: [batch_size, seq_len, d_model]
        x = x + self.pe[:, :x.size(1), :]
        return x


class TransformerModel(nn.Module):
    """Transformer模型用于时间序列预测，接收拼接后的时间序列数据"""
    def __init__(self, input_dim, d_model, nhead, num_layers, dropout=0.1):
        super(TransformerModel, self).__init__()
        self.input_dim = input_dim
        self.d_model = d_model
        
        # 输入映射层
        self.input_projection = nn.Linear(input_dim, d_model)
        
        # 位置编码
        self.pos_encoder = PositionalEncoding(d_model)
        
        # Transformer编码器层
        encoder_layers = nn.TransformerEncoderLayer(d_model=d_model, nhead=nhead, dropout=dropout)
        self.transformer_encoder = nn.TransformerEncoder(encoder_layers, num_layers=num_layers)
        
        # 输出映射层，将d_model映射回input_dim
        self.output_projection = nn.Linear(d_model, input_dim)
        
        # 初始化参数
        self._init_parameters()
    
    def _init_parameters(self):
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)
    
    def forward(self, src):
        # src: [batch_size, seq_len, input_dim]
        # 对于拼接的数据，seq_len=3（周、天、时）
        
        # 调整输入维度
        batch_size, seq_len, _ = src.size()
        
        # 映射到模型维度
        src = self.input_projection(src)  # [batch_size, seq_len, d_model]
        
        # 添加位置编码
        src = self.pos_encoder(src)  # [batch_size, seq_len, d_model]
        
        # 转置为Transformer期望的输入格式
        src = src.permute(1, 0, 2)  # [seq_len, batch_size, d_model]
        
        # 通过Transformer编码器
        output = self.transformer_encoder(src)  # [seq_len, batch_size, d_model]
        
        # 转回原始格式
        output = output.permute(1, 0, 2)  # [batch_size, seq_len, d_model]
        
        # 映射回原始维度
        output = self.output_projection(output)  # [batch_size, seq_len, input_dim]
        
        return output