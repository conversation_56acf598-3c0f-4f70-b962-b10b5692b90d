import torch
from torch import nn
import torch.nn.functional as F
import numpy as np
from model.GCN_layers import GraphConvolution


class FFTPeakPredictor(nn.Module):
    """FFT高峰期预测器 - 专门处理10个时间步的高峰期预测"""
    
    def __init__(self, time_steps=10, device='cpu'):
        super(FFTPeakPredictor, self).__init__()
        self.time_steps = time_steps
        self.device = device
        
        # FFT特征维度：10个时间步 -> 5个频率分量(幅度) + 5个频率分量(相位) = 10维
        self.fft_feature_dim = time_steps
        
        # 两层MLP进行端到端预测
        self.mlp = nn.Sequential(
            nn.Linear(self.fft_feature_dim, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(32, time_steps)  # 输出10个时间步的预测
        ).to(device)
    
    def extract_fft_features(self, sequences):
        """
        提取FFT特征
        Args:
            sequences: (batch_size, station_num, time_steps)
        Returns:
            fft_features: (batch_size, station_num, fft_feature_dim)
        """
        batch_size, station_num, time_steps = sequences.shape
        fft_features = torch.zeros(batch_size, station_num, self.fft_feature_dim).to(self.device)
        
        # 转换为numpy进行FFT处理
        sequences_np = sequences.cpu().detach().numpy()
        
        for b in range(batch_size):
            for s in range(station_num):
                sequence = sequences_np[b, s, :]
                
                # 应用FFT
                fft_result = np.fft.fft(sequence)
                
                # 提取幅度和相位
                magnitude = np.abs(fft_result)
                phase = np.angle(fft_result)
                
                # 只保留前一半频率（由于对称性）
                half_len = len(magnitude) // 2
                magnitude = magnitude[:half_len]
                phase = phase[:half_len]

                # 合并特征：幅度 + 相位
                features = np.concatenate([magnitude, phase])

                # 确保特征维度正确
                if len(features) > self.fft_feature_dim:
                    features = features[:self.fft_feature_dim]
                elif len(features) < self.fft_feature_dim:
                    # 用零填充
                    padded_features = np.zeros(self.fft_feature_dim)
                    padded_features[:len(features)] = features
                    features = padded_features

                fft_features[b, s, :] = torch.FloatTensor(features).to(self.device)
        
        return fft_features
    
    def forward(self, peak_sequences):
        """
        前向传播
        Args:
            peak_sequences: 高峰期序列 (batch_size, station_num, time_steps)
        Returns:
            predictions: 预测结果 (batch_size, station_num, time_steps)
        """
        # 提取FFT特征
        fft_features = self.extract_fft_features(peak_sequences)
        
        # 通过MLP进行端到端预测
        batch_size, station_num, _ = fft_features.shape
        
        # 重塑为 (batch_size * station_num, fft_feature_dim)
        fft_features_flat = fft_features.view(-1, self.fft_feature_dim)
        
        # MLP预测
        predictions_flat = self.mlp(fft_features_flat)  # (batch_size * station_num, time_steps)
        
        # 重塑回原始维度
        predictions = predictions_flat.view(batch_size, station_num, self.time_steps)
        
        return predictions


class GCNMultiScalePredictor(nn.Module):
    """GCN多尺度预测器 - 处理实时、日、周客流预测"""
    
    def __init__(self, time_lag, pre_len, station_num, device):
        super(GCNMultiScalePredictor, self).__init__()
        self.time_lag = time_lag
        self.pre_len = pre_len
        self.station_num = station_num
        self.device = device
        
        # 三个独立的GCN层处理不同时间尺度
        self.gcn_week = GraphConvolution(in_features=time_lag, out_features=time_lag).to(device)
        self.gcn_day = GraphConvolution(in_features=time_lag, out_features=time_lag).to(device)
        self.gcn_recent = GraphConvolution(in_features=time_lag, out_features=time_lag).to(device)
        
        # 多尺度特征融合层
        self.fusion_layer = nn.Linear(time_lag * 3, time_lag * 2).to(device)
        
        # 预测层
        self.prediction_layers = nn.Sequential(
            nn.Linear(time_lag * 2 * station_num, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, station_num * pre_len)
        ).to(device)
    
    def forward(self, inflow, adj):
        """
        前向传播
        Args:
            inflow: 输入数据 (batch_size, station_num, time_lag*3)
            adj: 邻接矩阵 (station_num, station_num)
        Returns:
            predictions: 预测结果 (batch_size, station_num, pre_len)
        """
        batch_size = inflow.size(0)
        
        # 分离三个时间尺度的数据
        inflow_week = inflow[:, :, 0:self.time_lag]                           # 周客流
        inflow_day = inflow[:, :, self.time_lag:self.time_lag*2]              # 日客流
        inflow_recent = inflow[:, :, self.time_lag*2:self.time_lag*3]         # 实时客流
        
        # GCN处理不同时间尺度
        gcn_week = self.gcn_week(x=inflow_week, adj=adj)
        gcn_day = self.gcn_day(x=inflow_day, adj=adj)
        gcn_recent = self.gcn_recent(x=inflow_recent, adj=adj)
        
        # 多尺度特征融合
        multi_scale_features = torch.cat([gcn_week, gcn_day, gcn_recent], dim=2)
        fused_features = F.relu(self.fusion_layer(multi_scale_features))
        
        # 预测
        output = fused_features.reshape(batch_size, -1)
        output = self.prediction_layers(output)
        output = output.reshape(batch_size, self.station_num, self.pre_len)
        
        return output


class GCN_FFT_FusionModel(nn.Module):
    """GCN+FFT融合模型 - 结合多尺度GCN和FFT高峰期预测"""
    
    def __init__(self, time_lag, pre_len, station_num, device, peak_time_steps=10):
        super(GCN_FFT_FusionModel, self).__init__()
        self.time_lag = time_lag
        self.pre_len = pre_len
        self.station_num = station_num
        self.device = device
        self.peak_time_steps = peak_time_steps
        
        # GCN多尺度预测器
        self.gcn_predictor = GCNMultiScalePredictor(time_lag, pre_len, station_num, device)
        
        # FFT高峰期预测器
        self.fft_predictor = FFTPeakPredictor(peak_time_steps, device)
        
        # 融合层 - 将GCN和FFT的预测结果融合
        self.fusion_weights = nn.Parameter(torch.tensor([0.7, 0.3]).to(device))  # 可学习的融合权重
        self.fusion_layer = nn.Linear(pre_len * 2, pre_len).to(device)
        
        # 自适应权重网络
        self.adaptive_weight_net = nn.Sequential(
            nn.Linear(time_lag * 3, 64),
            nn.ReLU(),
            nn.Linear(64, 2),
            nn.Softmax(dim=-1)
        ).to(device)
    
    def detect_peak_periods(self, inflow):
        """
        检测高峰期数据
        Args:
            inflow: 输入数据 (batch_size, station_num, time_lag*3)
        Returns:
            peak_sequences: 高峰期序列 (batch_size, station_num, peak_time_steps)
        """
        # 简化版：使用最近的时间段作为高峰期数据
        recent_data = inflow[:, :, self.time_lag*2:self.time_lag*3]  # 最近时间段
        
        # 如果time_lag等于peak_time_steps，直接使用
        if self.time_lag == self.peak_time_steps:
            return recent_data
        
        # 否则进行插值或截取
        if self.time_lag > self.peak_time_steps:
            # 截取前peak_time_steps个时间步
            return recent_data[:, :, :self.peak_time_steps]
        else:
            # 重复填充到peak_time_steps
            repeat_times = self.peak_time_steps // self.time_lag
            remainder = self.peak_time_steps % self.time_lag
            repeated = recent_data.repeat(1, 1, repeat_times)
            if remainder > 0:
                repeated = torch.cat([repeated, recent_data[:, :, :remainder]], dim=2)
            return repeated
    
    def forward(self, inflow, adj):
        """
        前向传播
        Args:
            inflow: 输入数据 (batch_size, station_num, time_lag*3)
            adj: 邻接矩阵 (station_num, station_num)
        Returns:
            fused_predictions: 融合预测结果 (batch_size, station_num, pre_len)
        """
        batch_size = inflow.size(0)
        
        # GCN多尺度预测
        gcn_predictions = self.gcn_predictor(inflow, adj)  # (batch_size, station_num, pre_len)
        
        # 检测并提取高峰期数据
        peak_sequences = self.detect_peak_periods(inflow)  # (batch_size, station_num, peak_time_steps)
        
        # FFT高峰期预测
        fft_predictions = self.fft_predictor(peak_sequences)  # (batch_size, station_num, peak_time_steps)
        
        # 将FFT预测结果调整到与GCN相同的输出维度
        # 简化处理：直接取平均值或选择特定时间步
        if fft_predictions.size(2) != self.pre_len:
            if self.pre_len == 1:
                # 如果预测长度是1，取所有时间步的平均值
                fft_predictions = fft_predictions.mean(dim=2, keepdim=True)  # (batch_size, station_num, 1)
            else:
                # 如果预测长度大于1，使用线性插值
                fft_predictions = F.interpolate(
                    fft_predictions.transpose(1, 2),
                    size=self.pre_len,
                    mode='linear',
                    align_corners=False
                ).transpose(1, 2)
        
        # 计算自适应融合权重
        input_features = inflow.mean(dim=1)  # (batch_size, time_lag*3)
        adaptive_weights = self.adaptive_weight_net(input_features)  # (batch_size, 2)
        
        # 扩展权重维度以匹配预测结果
        adaptive_weights = adaptive_weights.unsqueeze(1).unsqueeze(3)  # (batch_size, 1, 2, 1)
        
        # 堆叠预测结果
        stacked_predictions = torch.stack([gcn_predictions, fft_predictions], dim=2)  # (batch_size, station_num, 2, pre_len)
        
        # 应用自适应权重
        weighted_predictions = (stacked_predictions * adaptive_weights).sum(dim=2)  # (batch_size, station_num, pre_len)
        
        return weighted_predictions
    
    def get_model_info(self):
        """获取模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        info = {
            'model_name': 'GCN_FFT_FusionModel',
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'time_lag': self.time_lag,
            'pre_len': self.pre_len,
            'station_num': self.station_num,
            'peak_time_steps': self.peak_time_steps,
            'device': str(self.device)
        }
        return info
