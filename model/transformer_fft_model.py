import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math


class PositionalEncoding(nn.Module):
    """位置编码"""
    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        return x + self.pe[:x.size(0), :]


class TransformerPredictor(nn.Module):
    """Transformer客流预测器"""
    
    def __init__(self, time_lag, pre_len, station_num, d_model=128, nhead=8, num_layers=6, device='cpu'):
        super(TransformerPredictor, self).__init__()
        self.time_lag = time_lag
        self.pre_len = pre_len
        self.station_num = station_num
        self.d_model = d_model
        self.device = device
        
        # 输入投影层
        self.input_projection = nn.Linear(station_num, d_model).to(device)
        
        # 位置编码
        self.pos_encoder = PositionalEncoding(d_model).to(device)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 4,
            dropout=0.1,
            batch_first=True
        ).to(device)
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers).to(device)
        
        # 输出投影层
        self.output_projection = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, station_num * pre_len)
        ).to(device)
    
    def forward(self, x):
        """
        前向传播
        Args:
            x: 输入数据 (batch_size, time_lag*3, station_num)
        Returns:
            output: 预测结果 (batch_size, station_num, pre_len)
        """
        batch_size, seq_len, _ = x.shape
        
        # 输入投影
        x = self.input_projection(x)  # (batch_size, seq_len, d_model)
        
        # 位置编码
        x = x.transpose(0, 1)  # (seq_len, batch_size, d_model)
        x = self.pos_encoder(x)
        x = x.transpose(0, 1)  # (batch_size, seq_len, d_model)
        
        # Transformer编码
        encoded = self.transformer_encoder(x)  # (batch_size, seq_len, d_model)
        
        # 取最后一个时间步的输出
        last_output = encoded[:, -1, :]  # (batch_size, d_model)
        
        # 输出投影
        output = self.output_projection(last_output)  # (batch_size, station_num * pre_len)
        output = output.reshape(batch_size, self.station_num, self.pre_len)
        
        return output


class FFTPeakPredictor(nn.Module):
    """FFT高峰期预测器"""
    
    def __init__(self, peak_time_steps=10, station_num=276, device='cpu'):
        super(FFTPeakPredictor, self).__init__()
        self.peak_time_steps = peak_time_steps
        self.station_num = station_num
        self.device = device
        
        # FFT特征维度：peak_time_steps个时间步 -> peak_time_steps维特征
        self.fft_feature_dim = peak_time_steps
        
        # 两层MLP进行端到端预测
        self.mlp = nn.Sequential(
            nn.Linear(self.fft_feature_dim, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(32, 1)  # 输出单个预测值
        ).to(device)
    
    def extract_fft_features(self, sequences):
        """
        提取FFT特征
        Args:
            sequences: (batch_size, station_num, peak_time_steps)
        Returns:
            fft_features: (batch_size, station_num, fft_feature_dim)
        """
        batch_size, station_num, time_steps = sequences.shape
        fft_features = torch.zeros(batch_size, station_num, self.fft_feature_dim).to(self.device)
        
        # 转换为numpy进行FFT处理
        sequences_np = sequences.cpu().detach().numpy()
        
        for b in range(batch_size):
            for s in range(station_num):
                sequence = sequences_np[b, s, :]
                
                # 应用FFT
                fft_result = np.fft.fft(sequence)
                
                # 提取幅度和相位
                magnitude = np.abs(fft_result)
                phase = np.angle(fft_result)
                
                # 只保留前一半频率（由于对称性）
                half_len = len(magnitude) // 2
                magnitude = magnitude[:half_len]
                phase = phase[:half_len]
                
                # 合并特征：幅度 + 相位
                features = np.concatenate([magnitude, phase])
                
                # 确保特征维度正确
                if len(features) > self.fft_feature_dim:
                    features = features[:self.fft_feature_dim]
                elif len(features) < self.fft_feature_dim:
                    # 用零填充
                    padded_features = np.zeros(self.fft_feature_dim)
                    padded_features[:len(features)] = features
                    features = padded_features
                
                fft_features[b, s, :] = torch.FloatTensor(features).to(self.device)
        
        return fft_features
    
    def forward(self, peak_sequences):
        """
        前向传播
        Args:
            peak_sequences: 高峰期序列 (batch_size, station_num, peak_time_steps)
        Returns:
            predictions: 预测结果 (batch_size, station_num, 1)
        """
        # 提取FFT特征
        fft_features = self.extract_fft_features(peak_sequences)
        
        # 通过MLP进行端到端预测
        batch_size, station_num, _ = fft_features.shape
        
        # 重塑为 (batch_size * station_num, fft_feature_dim)
        fft_features_flat = fft_features.view(-1, self.fft_feature_dim)
        
        # MLP预测
        predictions_flat = self.mlp(fft_features_flat)  # (batch_size * station_num, 1)
        
        # 重塑回原始维度
        predictions = predictions_flat.view(batch_size, station_num, 1)
        
        return predictions


class TransformerFFTFusionModel(nn.Module):
    """Transformer+FFT融合模型"""
    
    def __init__(self, time_lag, pre_len, station_num, device, peak_time_steps=10):
        super(TransformerFFTFusionModel, self).__init__()
        self.time_lag = time_lag
        self.pre_len = pre_len
        self.station_num = station_num
        self.device = device
        self.peak_time_steps = peak_time_steps
        
        # Transformer预测器
        self.transformer_predictor = TransformerPredictor(
            time_lag, pre_len, station_num, device=device
        )
        
        # FFT高峰期预测器
        self.fft_predictor = FFTPeakPredictor(
            peak_time_steps, station_num, device=device
        )
        
        # 融合权重（可学习）
        self.fusion_weights = nn.Parameter(torch.tensor([0.7, 0.3]).to(device))
        
        # 自适应融合网络
        self.adaptive_fusion = nn.Sequential(
            nn.Linear(time_lag * 3, 64),
            nn.ReLU(),
            nn.Linear(64, 2),
            nn.Softmax(dim=-1)
        ).to(device)
    
    def detect_peak_periods(self, inflow):
        """
        检测高峰期数据
        Args:
            inflow: 输入数据 (batch_size, time_lag*3, station_num)
        Returns:
            peak_sequences: 高峰期序列 (batch_size, station_num, peak_time_steps)
        """
        # 使用最近的时间段作为高峰期数据
        recent_data = inflow[:, self.time_lag*2:self.time_lag*3, :]  # 最近时间段
        
        # 转置以匹配FFT预测器的输入格式
        peak_sequences = recent_data.transpose(1, 2)  # (batch_size, station_num, time_lag)
        
        # 如果time_lag等于peak_time_steps，直接使用
        if self.time_lag == self.peak_time_steps:
            return peak_sequences
        
        # 否则进行调整
        if self.time_lag > self.peak_time_steps:
            # 截取前peak_time_steps个时间步
            return peak_sequences[:, :, :self.peak_time_steps]
        else:
            # 重复填充到peak_time_steps
            repeat_times = self.peak_time_steps // self.time_lag
            remainder = self.peak_time_steps % self.time_lag
            repeated = peak_sequences.repeat(1, 1, repeat_times)
            if remainder > 0:
                repeated = torch.cat([repeated, peak_sequences[:, :, :remainder]], dim=2)
            return repeated
    
    def forward(self, inflow):
        """
        前向传播
        Args:
            inflow: 输入数据 (batch_size, time_lag*3, station_num)
        Returns:
            fused_predictions: 融合预测结果 (batch_size, station_num, pre_len)
        """
        batch_size = inflow.size(0)
        
        # Transformer预测
        transformer_predictions = self.transformer_predictor(inflow)  # (batch_size, station_num, pre_len)
        
        # 检测并提取高峰期数据
        peak_sequences = self.detect_peak_periods(inflow)  # (batch_size, station_num, peak_time_steps)
        
        # FFT高峰期预测
        fft_predictions = self.fft_predictor(peak_sequences)  # (batch_size, station_num, 1)
        
        # 如果预测长度不匹配，调整FFT预测维度
        if fft_predictions.size(2) != self.pre_len:
            if self.pre_len > 1:
                # 重复FFT预测以匹配预测长度
                fft_predictions = fft_predictions.repeat(1, 1, self.pre_len)
        
        # 计算自适应融合权重
        input_features = inflow.mean(dim=2)  # (batch_size, time_lag*3)
        adaptive_weights = self.adaptive_fusion(input_features)  # (batch_size, 2)
        
        # 扩展权重维度
        adaptive_weights = adaptive_weights.unsqueeze(1).unsqueeze(3)  # (batch_size, 1, 2, 1)
        
        # 堆叠预测结果
        stacked_predictions = torch.stack([transformer_predictions, fft_predictions], dim=2)  # (batch_size, station_num, 2, pre_len)
        
        # 应用自适应权重
        fused_predictions = (stacked_predictions * adaptive_weights).sum(dim=2)  # (batch_size, station_num, pre_len)
        
        return fused_predictions
    
    def get_model_info(self):
        """获取模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        info = {
            'model_name': 'TransformerFFTFusionModel',
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'time_lag': self.time_lag,
            'pre_len': self.pre_len,
            'station_num': self.station_num,
            'peak_time_steps': self.peak_time_steps,
            'device': str(self.device)
        }
        return info


def test_transformer_fft_model():
    """测试Transformer+FFT模型"""
    print("Testing Transformer+FFT Model...")

    # 模型参数
    time_lag = 10
    pre_len = 1
    station_num = 276
    batch_size = 4
    device = torch.device('cpu')

    # 创建模型
    model = TransformerFFTFusionModel(time_lag, pre_len, station_num, device)

    # 创建测试数据
    test_inflow = torch.randn(batch_size, time_lag * 3, station_num)

    # 前向传播
    with torch.no_grad():
        output = model(test_inflow)

    print(f"Input shape: {test_inflow.shape}")
    print(f"Output shape: {output.shape}")
    print(f"Expected output shape: ({batch_size}, {station_num}, {pre_len})")

    # 打印模型信息
    model_info = model.get_model_info()
    print("\nModel Information:")
    for key, value in model_info.items():
        print(f"  {key}: {value}")

    print("Transformer+FFT Model test completed successfully!")


if __name__ == "__main__":
    test_transformer_fft_model()
