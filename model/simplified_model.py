import torch
from torch import nn
import torch.nn.functional as F
from model.GCN_layers import GraphConvolution
from model.Transformer_layers import TransformerModel


class SimplifiedModel(nn.Module):
	def __init__(self, time_lag, pre_len, station_num, device):
		super().__init__()
		self.time_lag = time_lag
		self.pre_len = pre_len
		self.station_num = station_num
		self.device = device
		
		# 简化的GCN模块 - 只使用一个GCN层
		self.GCN = GraphConvolution(in_features=self.time_lag, out_features=self.time_lag).to(self.device)
		
		# 简化的Transformer模块
		self.transformer = TransformerModel(input_dim=self.time_lag, d_model=64, nhead=8, num_layers=2, dropout=0.1).to(self.device)
		
		# 特征融合层 - 简化输入维度
		self.fusion_layer = nn.Linear(in_features=self.time_lag*6, out_features=self.time_lag*2).to(self.device)
		
		# 简化的全连接层
		self.linear1 = nn.Linear(in_features=self.time_lag*2*self.station_num, out_features=512).to(self.device)
		self.linear2 = nn.Linear(in_features=512, out_features=256).to(self.device)
		self.linear3 = nn.Linear(in_features=256, out_features=self.station_num*self.pre_len).to(self.device)
		
		# Dropout层
		self.dropout = nn.Dropout(0.3).to(self.device)

	def forward(self, inflow, outflow, adj):
		inflow = inflow.to(self.device)
		outflow = outflow.to(self.device)
		adj = adj.to(self.device)
		
		# 分离三个时间段的数据 - inflow
		inflow_week = inflow[:, :, 0:self.time_lag]
		inflow_day = inflow[:, :, self.time_lag:self.time_lag*2]
		inflow_time = inflow[:, :, self.time_lag*2:self.time_lag*3]
		
		# 分离三个时间段的数据 - outflow
		outflow_week = outflow[:, :, 0:self.time_lag]
		outflow_day = outflow[:, :, self.time_lag:self.time_lag*2]
		outflow_time = outflow[:, :, self.time_lag*2:self.time_lag*3]
		
		# GCN处理 - 对所有时间段使用同一个GCN
		gcn_inflow_week = self.GCN(x=inflow_week, adj=adj)
		gcn_inflow_day = self.GCN(x=inflow_day, adj=adj)
		gcn_inflow_time = self.GCN(x=inflow_time, adj=adj)
		
		# Transformer处理 - 处理inflow和outflow的时间序列
		batch_size, station_num, _ = inflow_week.size()
		
		# 将inflow的三个时间段拼接
		inflow_combined = torch.cat([inflow_week, inflow_day, inflow_time], dim=2)  # (batch, station, 30)
		outflow_combined = torch.cat([outflow_week, outflow_day, outflow_time], dim=2)  # (batch, station, 30)
		
		# 重塑数据以适应Transformer
		inflow_reshaped = inflow_combined.reshape(-1, 3, self.time_lag)  # (batch*station, 3, time_lag)
		outflow_reshaped = outflow_combined.reshape(-1, 3, self.time_lag)  # (batch*station, 3, time_lag)
		
		# Transformer处理
		trans_inflow = self.transformer(inflow_reshaped)  # (batch*station, 3, time_lag)
		trans_outflow = self.transformer(outflow_reshaped)  # (batch*station, 3, time_lag)
		
		# 重塑回原始维度
		trans_inflow = trans_inflow.reshape(batch_size, station_num, 3*self.time_lag)
		trans_outflow = trans_outflow.reshape(batch_size, station_num, 3*self.time_lag)
		
		# 特征融合 - 简化版本
		combined_features = torch.cat([
			gcn_inflow_week, gcn_inflow_day, gcn_inflow_time,
			trans_inflow, trans_outflow
		], dim=2)  # (batch, station, time_lag*6)
		
		fused_features = F.relu(self.fusion_layer(combined_features))  # (batch, station, time_lag*2)
		
		# 全连接层处理
		output = fused_features.reshape(batch_size, -1)  # (batch, station*time_lag*2)
		output = self.dropout(F.relu(self.linear1(output)))  # (batch, 512)
		output = self.dropout(F.relu(self.linear2(output)))  # (batch, 256)
		output = self.linear3(output)  # (batch, station*pre_len)
		output = output.reshape(batch_size, self.station_num, self.pre_len)  # (batch, station, pre_len)
		
		return output


class Model(SimplifiedModel):
	"""为了保持兼容性，使用SimplifiedModel作为默认Model"""
	pass
