import torch
import torch.nn as nn
import torch.nn.functional as F


class MLPPredictor(nn.Module):
    """简单的MLP客流预测模型"""
    
    def __init__(self, time_lag, pre_len, station_num, hidden_dims=[512, 256, 128], dropout=0.2, device='cpu'):
        super(MLPPredictor, self).__init__()
        self.time_lag = time_lag
        self.pre_len = pre_len
        self.station_num = station_num
        self.device = device
        
        # 输入维度：station_num * time_lag * 3 (周、日、近期)
        input_dim = station_num * time_lag * 3
        # 输出维度：station_num * pre_len
        output_dim = station_num * pre_len
        
        # 构建MLP网络
        layers = []
        
        # 输入层
        layers.append(nn.Linear(input_dim, hidden_dims[0]))
        layers.append(nn.ReLU())
        layers.append(nn.Dropout(dropout))
        
        # 隐藏层
        for i in range(len(hidden_dims) - 1):
            layers.append(nn.Linear(hidden_dims[i], hidden_dims[i + 1]))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout))
        
        # 输出层
        layers.append(nn.Linear(hidden_dims[-1], output_dim))
        
        # 组合所有层
        self.mlp = nn.Sequential(*layers).to(device)
        
        # 批归一化层（可选）
        self.batch_norm = nn.BatchNorm1d(input_dim).to(device)
        
    def forward(self, x):
        """
        前向传播
        Args:
            x: 输入数据 (batch_size, station_num, time_lag*3)
        Returns:
            output: 预测结果 (batch_size, station_num, pre_len)
        """
        batch_size = x.size(0)
        
        # 展平输入数据
        x_flat = x.view(batch_size, -1)  # (batch_size, station_num * time_lag * 3)
        
        # 批归一化
        x_norm = self.batch_norm(x_flat)
        
        # MLP前向传播
        output_flat = self.mlp(x_norm)  # (batch_size, station_num * pre_len)
        
        # 重塑为目标形状
        output = output_flat.view(batch_size, self.station_num, self.pre_len)
        
        return output
    
    def get_model_info(self):
        """获取模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        info = {
            'model_name': 'MLPPredictor',
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'time_lag': self.time_lag,
            'pre_len': self.pre_len,
            'station_num': self.station_num,
            'device': str(self.device)
        }
        return info


def test_mlp_model():
    """测试MLP模型"""
    print("Testing MLP Model...")
    
    # 模型参数
    time_lag = 10
    pre_len = 1
    station_num = 276
    batch_size = 4
    device = torch.device('cpu')
    
    # 创建模型
    model = MLPPredictor(time_lag, pre_len, station_num, device=device)
    
    # 创建测试数据
    test_input = torch.randn(batch_size, station_num, time_lag * 3)
    
    # 前向传播
    with torch.no_grad():
        output = model(test_input)
    
    print(f"Input shape: {test_input.shape}")
    print(f"Output shape: {output.shape}")
    print(f"Expected output shape: ({batch_size}, {station_num}, {pre_len})")
    
    # 打印模型信息
    model_info = model.get_model_info()
    print("\nModel Information:")
    for key, value in model_info.items():
        print(f"  {key}: {value}")
    
    # 检查输出形状
    expected_shape = (batch_size, station_num, pre_len)
    if output.shape == expected_shape:
        print("✓ MLP Model test passed!")
        return True
    else:
        print(f"✗ Output shape incorrect: expected {expected_shape}, got {output.shape}")
        return False


if __name__ == "__main__":
    test_mlp_model()
