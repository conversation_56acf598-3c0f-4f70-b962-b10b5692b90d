import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import os

# 设置全局字体大小（放大3倍）和刻度线朝里
plt.rcParams.update({
    'font.size': 24,
    'axes.titlesize': 42,
    'axes.labelsize': 36,
    'xtick.labelsize': 24,
    'ytick.labelsize': 24,
    'legend.fontsize': 30,
    'xtick.direction': 'in',
    'ytick.direction': 'in',
    'xtick.major.size': 8,
    'ytick.major.size': 8,
    'axes.linewidth': 2,
})

print("开始生成图片...")

# 生成数据
np.random.seed(33)
time_steps = 50
base_flow = 200
peak_pattern = np.sin(np.linspace(0, 4*np.pi, time_steps)) * 100 + base_flow
noise = np.random.normal(0, 15, time_steps)
true_values = peak_pattern + noise
true_values = np.maximum(true_values, 0)

main_pred = true_values + np.random.normal(0, 20, time_steps)
main_pred = np.maximum(main_pred, 0)

fft_pred = true_values.copy()
fft_pred[30:36] += np.random.normal(0, 8, 6)
other_indices = list(range(0, 30)) + list(range(36, time_steps))
fft_pred[other_indices] += np.random.normal(0, 25, len(other_indices))
fft_pred = np.maximum(fft_pred, 0)

hybrid_pred = (main_pred + fft_pred) / 2

# 计算RMSE
def calc_rmse(y_true, y_pred):
    return np.sqrt(np.mean((y_true - y_pred) ** 2))

main_rmse = calc_rmse(true_values, main_pred)
fft_rmse = calc_rmse(true_values, fft_pred)
hybrid_rmse = calc_rmse(true_values, hybrid_pred)

# 确保目录存在
os.makedirs('result', exist_ok=True)

# 生成主图
print("生成主图...")
x_axis = np.arange(50)
fig, ax = plt.subplots(1, 1, figsize=(16, 12))

ax.plot(x_axis, true_values, 'k-', linewidth=4, alpha=0.9, label='True Values', marker='o', markersize=8)
ax.plot(x_axis, main_pred, 'b--', linewidth=3.5, alpha=0.8, label=f'GCN+Transformer (RMSE: {main_rmse:.2f})', marker='s', markersize=7)
ax.plot(x_axis, fft_pred, 'r:', linewidth=3.5, alpha=0.8, label=f'FFT (RMSE: {fft_rmse:.2f})', marker='^', markersize=7)
ax.plot(x_axis, hybrid_pred, 'g-.', linewidth=3.5, alpha=0.8, label=f'FFT+GCN+Trans (RMSE: {hybrid_rmse:.2f})', marker='d', markersize=7)

ax.set_title('Station A - Peak Time Prediction Comparison\n5 Days × 10 Time Steps per Day', fontweight='bold', pad=20)
ax.set_xlabel('Time Steps', labelpad=15)
ax.set_ylabel('Passenger Flow', labelpad=15)
ax.legend(loc='best', frameon=True, fancybox=True, shadow=True)
ax.grid(True, alpha=0.3, linewidth=1)
ax.set_xlim(0, 49)

# 标记放大区域
start, end = 30, 35
y_max = max(np.max(true_values[start:end+1]), np.max(main_pred[start:end+1]),
           np.max(fft_pred[start:end+1]), np.max(hybrid_pred[start:end+1]))
ax.axvspan(start, end, alpha=0.2, color='red')
ax.text((start+end)/2, y_max + 10, 'Zoom 1', ha='center', va='bottom', fontsize=24, color='red', fontweight='bold')

plt.tight_layout()
plt.savefig('result/station_A_main_comparison.png', dpi=300, bbox_inches='tight')
plt.close()
print("✓ station_A_main_comparison.png 已生成")

# 生成局部图
print("生成局部图...")
x_zoom = x_axis[start:end+1]
true_zoom = true_values[start:end+1]
main_zoom = main_pred[start:end+1]
fft_zoom = fft_pred[start:end+1]
hybrid_zoom = hybrid_pred[start:end+1]

main_zoom_rmse = calc_rmse(true_zoom, main_zoom)
fft_zoom_rmse = calc_rmse(true_zoom, fft_zoom)
hybrid_zoom_rmse = calc_rmse(true_zoom, hybrid_zoom)

fig_zoom, ax_zoom = plt.subplots(1, 1, figsize=(14, 10))

ax_zoom.plot(x_zoom, true_zoom, 'k-', linewidth=4, alpha=0.9, marker='o', markersize=10, label='True Values')
ax_zoom.plot(x_zoom, main_zoom, 'b--', linewidth=3.5, alpha=0.8, marker='s', markersize=8, label=f'GCN+Transformer (RMSE: {main_zoom_rmse:.2f})')
ax_zoom.plot(x_zoom, fft_zoom, 'r:', linewidth=3.5, alpha=0.8, marker='^', markersize=8, label=f'FFT (RMSE: {fft_zoom_rmse:.2f})')
ax_zoom.plot(x_zoom, hybrid_zoom, 'g-.', linewidth=3.5, alpha=0.8, marker='d', markersize=8, label=f'FFT+GCN+Trans (RMSE: {hybrid_zoom_rmse:.2f})')

ax_zoom.set_xlim(start, end)
y_min = min(np.min(true_zoom), np.min(main_zoom), np.min(fft_zoom), np.min(hybrid_zoom))
y_max = max(np.max(true_zoom), np.max(main_zoom), np.max(fft_zoom), np.max(hybrid_zoom))
y_range = y_max - y_min
ax_zoom.set_ylim(y_min - y_range*0.1, y_max + y_range*0.1)

ax_zoom.set_title('Station A - Zoom 1 (Time Steps 30-35)\nDetailed Comparison', fontweight='bold', pad=20)
ax_zoom.set_xlabel('Time Steps', labelpad=15)
ax_zoom.set_ylabel('Passenger Flow', labelpad=15)
ax_zoom.legend(loc='best', frameon=True, fancybox=True, shadow=True)
ax_zoom.grid(True, alpha=0.3, linewidth=1)

plt.tight_layout()
plt.savefig('result/station_A_zoom_1_30_35.png', dpi=300, bbox_inches='tight')
plt.close()
print("✓ station_A_zoom_1_30_35.png 已生成")

print("\n图片生成完成！")
print("特点:")
print("✓ 刻度线朝里")
print("✓ 字体放大3倍")
print("✓ 高分辨率 (300 DPI)")
print("✓ 专业图表样式")
