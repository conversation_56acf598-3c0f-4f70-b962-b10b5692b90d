import numpy as np
import os
import time
import torch
from torch import nn
import sys

# 添加当前目录到Python路径
if '.' not in sys.path:
    sys.path.append('.')

# 可选导入
try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_AVAILABLE = True
except ImportError:
    print("Warning: TensorBoard not available. Logging will be disabled.")
    TENSORBOARD_AVAILABLE = False

try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    print("Warning: Matplotlib not available. Visualization will be disabled.")
    MATPLOTLIB_AVAILABLE = False

# 导入模块
try:
    from model.transformer_fft_model import TransformerFFTFusionModel, TransformerPredictor, FFTPeakPredictor
    from data.get_inflow_only_dataloader import get_inflow_only_dataloader
    from utils.earlystopping import EarlyStopping
    print("All modules imported successfully!")
except ImportError as e:
    print(f"Import error: {e}")
    print("Please check if all required files exist in the correct directories.")
    exit(1)


def calculate_metrics(predictions, true_values):
    """计算评估指标"""
    # 确保数据为numpy数组
    if isinstance(predictions, torch.Tensor):
        predictions = predictions.cpu().numpy()
    if isinstance(true_values, torch.Tensor):
        true_values = true_values.cpu().numpy()
    
    # 展平数据
    pred_flat = predictions.reshape(-1)
    true_flat = true_values.reshape(-1)
    
    # 计算指标
    rmse = np.sqrt(np.mean((pred_flat - true_flat) ** 2))
    mae = np.mean(np.abs(pred_flat - true_flat))
    wmape = np.sum(np.abs(true_flat - pred_flat)) / np.sum(true_flat) * 100
    
    return rmse, mae, wmape


def train_transformer_model(device, time_lag, pre_len, station_num, train_loader, val_loader, 
                          max_epochs=200, lr=0.001, save_dir='./save_model/transformer'):
    """训练Transformer模型"""
    print("\n" + "=" * 80)
    print("Training Transformer Model...")
    print("=" * 80)
    
    # 创建Transformer模型
    transformer_model = TransformerPredictor(time_lag, pre_len, station_num, device=device)
    transformer_model = transformer_model.to(device)
    
    # 优化器和损失函数
    optimizer = torch.optim.Adam(transformer_model.parameters(), lr=lr)
    mse_loss = torch.nn.MSELoss().to(device)
    
    # 早停机制
    early_stopping = EarlyStopping(patience=10, verbose=True)
    
    # 训练循环
    best_val_loss = float('inf')
    
    for epoch in range(max_epochs):
        # 训练阶段
        transformer_model.train()
        train_loss = 0
        train_batches = 0
        
        for i_batch, (train_X, train_Y) in enumerate(train_loader):
            # 调整输入格式：从 (batch, station, time*3) 到 (batch, time*3, station)
            train_X = train_X.transpose(1, 2).type(torch.float32).to(device)
            train_Y = train_Y.type(torch.float32).to(device)
            
            # 前向传播
            prediction = transformer_model(train_X)
            loss = mse_loss(prediction, train_Y)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_batches += 1
        
        # 验证阶段
        transformer_model.eval()
        val_loss = 0
        val_batches = 0
        
        with torch.no_grad():
            for i_batch, (val_X, val_Y) in enumerate(val_loader):
                val_X = val_X.transpose(1, 2).type(torch.float32).to(device)
                val_Y = val_Y.type(torch.float32).to(device)
                
                prediction = transformer_model(val_X)
                loss = mse_loss(prediction, val_Y)
                
                val_loss += loss.item()
                val_batches += 1
        
        # 计算平均损失
        avg_train_loss = train_loss / train_batches
        avg_val_loss = val_loss / val_batches
        
        # 打印训练信息
        if epoch % 10 == 0 or epoch < 10:
            print(f'Epoch {epoch:4d}: Train Loss = {avg_train_loss:.6f}, Val Loss = {avg_val_loss:.6f}')
        
        # 早停检查
        if epoch > 0:
            model_dict = transformer_model.state_dict()
            early_stopping(avg_val_loss, model_dict, transformer_model, epoch, save_dir)
            if early_stopping.early_stop:
                print(f"Early stopping at epoch {epoch}")
                break
    
    print("Transformer training completed!")

    # 确保返回正确的模型路径
    best_model_path = getattr(early_stopping, 'best_model_path', None)
    if best_model_path is None:
        # 如果没有保存的模型路径，创建一个默认路径
        import time
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        best_model_path = f"{save_dir}/transformer_final_{timestamp}.pth"
        torch.save(transformer_model.state_dict(), best_model_path)
        print(f"Saved final model to: {best_model_path}")

    return transformer_model, best_model_path


def train_fft_model(device, peak_time_steps, station_num, train_loader, val_loader,
                   max_epochs=200, lr=0.001, save_dir='./save_model/fft'):
    """训练FFT模型"""
    print("\n" + "=" * 80)
    print("Training FFT Peak Predictor...")
    print("=" * 80)
    
    # 创建FFT模型
    fft_model = FFTPeakPredictor(peak_time_steps, station_num, device=device)
    fft_model = fft_model.to(device)
    
    # 优化器和损失函数
    optimizer = torch.optim.Adam(fft_model.parameters(), lr=lr)
    mse_loss = torch.nn.MSELoss().to(device)
    
    # 早停机制
    early_stopping = EarlyStopping(patience=10, verbose=True)
    
    # 训练循环
    for epoch in range(max_epochs):
        # 训练阶段
        fft_model.train()
        train_loss = 0
        train_batches = 0
        
        for i_batch, (train_X, train_Y) in enumerate(train_loader):
            train_X = train_X.type(torch.float32).to(device)
            train_Y = train_Y.type(torch.float32).to(device)
            
            # 提取高峰期数据（最近10个时间步）
            peak_data = train_X[:, :, -peak_time_steps:]  # (batch, station, peak_time_steps)
            
            # 前向传播
            prediction = fft_model(peak_data)
            loss = mse_loss(prediction, train_Y)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_batches += 1
        
        # 验证阶段
        fft_model.eval()
        val_loss = 0
        val_batches = 0
        
        with torch.no_grad():
            for i_batch, (val_X, val_Y) in enumerate(val_loader):
                val_X = val_X.type(torch.float32).to(device)
                val_Y = val_Y.type(torch.float32).to(device)
                
                peak_data = val_X[:, :, -peak_time_steps:]
                prediction = fft_model(peak_data)
                loss = mse_loss(prediction, val_Y)
                
                val_loss += loss.item()
                val_batches += 1
        
        # 计算平均损失
        avg_train_loss = train_loss / train_batches
        avg_val_loss = val_loss / val_batches
        
        # 打印训练信息
        if epoch % 10 == 0 or epoch < 10:
            print(f'Epoch {epoch:4d}: Train Loss = {avg_train_loss:.6f}, Val Loss = {avg_val_loss:.6f}')
        
        # 早停检查
        if epoch > 0:
            model_dict = fft_model.state_dict()
            early_stopping(avg_val_loss, model_dict, fft_model, epoch, save_dir)
            if early_stopping.early_stop:
                print(f"Early stopping at epoch {epoch}")
                break
    
    print("FFT training completed!")

    # 确保返回正确的模型路径
    best_model_path = getattr(early_stopping, 'best_model_path', None)
    if best_model_path is None:
        # 如果没有保存的模型路径，创建一个默认路径
        import time
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        best_model_path = f"{save_dir}/fft_final_{timestamp}.pth"
        torch.save(fft_model.state_dict(), best_model_path)
        print(f"Saved final model to: {best_model_path}")

    return fft_model, best_model_path


def evaluate_and_visualize(transformer_model, fft_model, test_loader, device, max_inflow, min_inflow, 
                          time_lag, peak_time_steps, station_num):
    """评估模型并生成可视化"""
    print("\n" + "=" * 80)
    print("Model Evaluation and Visualization...")
    print("=" * 80)
    
    transformer_model.eval()
    fft_model.eval()
    
    transformer_predictions = []
    fft_predictions = []
    true_values = []
    true_values_original = []
    
    with torch.no_grad():
        for i_batch, (test_X, test_Y, test_Y_orig) in enumerate(test_loader):
            test_X = test_X.type(torch.float32).to(device)
            test_Y = test_Y.type(torch.float32).to(device)
            
            # Transformer预测
            transformer_X = test_X.transpose(1, 2)  # (batch, time*3, station)
            transformer_pred = transformer_model(transformer_X)
            
            # FFT预测
            peak_data = test_X[:, :, -peak_time_steps:]  # (batch, station, peak_time_steps)
            fft_pred = fft_model(peak_data)
            
            # 保存预测结果
            transformer_predictions.extend(transformer_pred.cpu().numpy())
            fft_predictions.extend(fft_pred.cpu().numpy())
            true_values.extend(test_Y.cpu().numpy())
            true_values_original.extend(test_Y_orig.numpy())
    
    # 转换为numpy数组
    transformer_predictions = np.array(transformer_predictions)
    fft_predictions = np.array(fft_predictions)
    true_values = np.array(true_values)
    true_values_original = np.array(true_values_original)
    
    # 反归一化
    transformer_orig = transformer_predictions * (max_inflow - min_inflow) + min_inflow
    fft_orig = fft_predictions * (max_inflow - min_inflow) + min_inflow
    
    # 融合预测（简单加权平均）
    fusion_weight_transformer = 0.7
    fusion_weight_fft = 0.3
    fusion_orig = fusion_weight_transformer * transformer_orig + fusion_weight_fft * fft_orig
    
    # 确保非负值
    transformer_orig = np.maximum(transformer_orig, 0)
    fft_orig = np.maximum(fft_orig, 0)
    fusion_orig = np.maximum(fusion_orig, 0)
    true_values_original = np.maximum(true_values_original, 0)
    
    # 计算评估指标
    transformer_rmse, transformer_mae, transformer_wmape = calculate_metrics(transformer_orig, true_values_original)
    fft_rmse, fft_mae, fft_wmape = calculate_metrics(fft_orig, true_values_original)
    fusion_rmse, fusion_mae, fusion_wmape = calculate_metrics(fusion_orig, true_values_original)
    
    print(f"Transformer Model Performance:")
    print(f"  RMSE: {transformer_rmse:.4f}")
    print(f"  MAE:  {transformer_mae:.4f}")
    print(f"  WMAPE: {transformer_wmape:.4f}%")
    
    print(f"\nFFT Model Performance:")
    print(f"  RMSE: {fft_rmse:.4f}")
    print(f"  MAE:  {fft_mae:.4f}")
    print(f"  WMAPE: {fft_wmape:.4f}%")
    
    print(f"\nFusion Model Performance:")
    print(f"  RMSE: {fusion_rmse:.4f}")
    print(f"  MAE:  {fusion_mae:.4f}")
    print(f"  WMAPE: {fusion_wmape:.4f}%")
    
    # 保存结果
    result_dir = 'result'
    os.makedirs(result_dir, exist_ok=True)
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    
    np.savetxt(f'{result_dir}/transformer_predictions_{timestamp}.txt', transformer_orig.reshape(-1, station_num), fmt='%.2f')
    np.savetxt(f'{result_dir}/fft_predictions_{timestamp}.txt', fft_orig.reshape(-1, station_num), fmt='%.2f')
    np.savetxt(f'{result_dir}/fusion_predictions_{timestamp}.txt', fusion_orig.reshape(-1, station_num), fmt='%.2f')
    np.savetxt(f'{result_dir}/true_values_{timestamp}.txt', true_values_original.reshape(-1, station_num), fmt='%.2f')
    
    # 可视化关键车站预测对比
    if MATPLOTLIB_AVAILABLE:
        key_stations = [0, 50, 100, 150, 200]  # 选择几个关键车站
        num_samples = min(50, len(true_values_original))
        
        plt.figure(figsize=(20, 12))
        for i, station_idx in enumerate(key_stations):
            plt.subplot(2, 3, i+1)
            plt.plot(true_values_original[:num_samples, station_idx, 0], 'b-', label='True', linewidth=2)
            plt.plot(transformer_orig[:num_samples, station_idx, 0], 'r--', label='Transformer', linewidth=2)
            plt.plot(fft_orig[:num_samples, station_idx, 0], 'g:', label='FFT', linewidth=2)
            plt.plot(fusion_orig[:num_samples, station_idx, 0], 'm-', label='Fusion', linewidth=2)
            plt.title(f'Station {station_idx} - Prediction Comparison')
            plt.xlabel('Time Steps')
            plt.ylabel('Flow')
            plt.legend()
            plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plot_file = f'{result_dir}/transformer_fft_comparison_{timestamp}.png'
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        print(f"\nVisualization saved: {plot_file}")
    
    return {
        'transformer': {'rmse': transformer_rmse, 'mae': transformer_mae, 'wmape': transformer_wmape},
        'fft': {'rmse': fft_rmse, 'mae': fft_mae, 'wmape': fft_wmape},
        'fusion': {'rmse': fusion_rmse, 'mae': fusion_mae, 'wmape': fusion_wmape}
    }


def main():
    """主函数"""
    # 设备配置
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # 模型参数配置
    time_interval = 15
    time_lag = 10
    tg_in_one_day = 72
    forecast_day_number = 5
    pre_len = 1
    batch_size = 32
    station_num = 276
    peak_time_steps = 10
    
    print("=" * 80)
    print("Transformer + FFT Metro Flow Prediction System")
    print("基于Transformer和FFT的地铁客流预测系统")
    print("=" * 80)
    print(f"Configuration:")
    print(f"  - Time Lag: {time_lag}")
    print(f"  - Prediction Length: {pre_len}")
    print(f"  - Station Number: {station_num}")
    print(f"  - Peak Time Steps: {peak_time_steps}")
    print(f"  - Batch Size: {batch_size}")
    print("=" * 80)
    
    # 加载数据
    print("Loading data...")
    train_loader, val_loader, test_loader, max_inflow, min_inflow = get_inflow_only_dataloader(
        time_interval=time_interval, 
        time_lag=time_lag, 
        tg_in_one_day=tg_in_one_day, 
        forecast_day_number=forecast_day_number, 
        pre_len=pre_len, 
        batch_size=batch_size
    )
    
    # 创建保存目录
    timestamp = time.strftime("%Y_%m_%d_%H_%M_%S")
    transformer_save_dir = f'./save_model/transformer_{timestamp}'
    fft_save_dir = f'./save_model/fft_{timestamp}'
    os.makedirs(transformer_save_dir, exist_ok=True)
    os.makedirs(fft_save_dir, exist_ok=True)
    
    # 训练Transformer模型
    transformer_model, transformer_model_path = train_transformer_model(
        device, time_lag, pre_len, station_num, train_loader, val_loader,
        max_epochs=200, lr=0.001, save_dir=transformer_save_dir
    )
    
    # 训练FFT模型
    fft_model, fft_model_path = train_fft_model(
        device, peak_time_steps, station_num, train_loader, val_loader,
        max_epochs=200, lr=0.001, save_dir=fft_save_dir
    )
    
    # 评估和可视化
    results = evaluate_and_visualize(
        transformer_model, fft_model, test_loader, device, max_inflow, min_inflow,
        time_lag, peak_time_steps, station_num
    )
    
    print("\n" + "=" * 80)
    print("Training and Evaluation Completed!")
    print("=" * 80)
    print("Final Results Summary:")
    print(f"  Transformer - RMSE: {results['transformer']['rmse']:.4f}, MAE: {results['transformer']['mae']:.4f}, WMAPE: {results['transformer']['wmape']:.4f}%")
    print(f"  FFT        - RMSE: {results['fft']['rmse']:.4f}, MAE: {results['fft']['mae']:.4f}, WMAPE: {results['fft']['wmape']:.4f}%")
    print(f"  Fusion     - RMSE: {results['fusion']['rmse']:.4f}, MAE: {results['fusion']['mae']:.4f}, WMAPE: {results['fusion']['wmape']:.4f}%")
    print("=" * 80)


if __name__ == "__main__":
    main()
