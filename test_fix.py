"""
测试修复后的EarlyStopping和训练函数
"""
import torch
import numpy as np
import os
import sys

# 添加当前目录到Python路径
if '.' not in sys.path:
    sys.path.append('.')

def test_earlystopping_fix():
    """测试EarlyStopping修复"""
    print("Testing EarlyStopping fix...")
    
    try:
        from utils.earlystopping import EarlyStopping
        
        # 创建EarlyStopping实例
        early_stopping = EarlyStopping(patience=5, verbose=True)
        
        # 检查是否有best_model_path属性
        if hasattr(early_stopping, 'best_model_path'):
            print("  ✓ EarlyStopping has best_model_path attribute")
        else:
            print("  ✗ EarlyStopping missing best_model_path attribute")
            return False
        
        # 测试保存检查点
        test_save_dir = './test_save'
        os.makedirs(test_save_dir, exist_ok=True)
        
        # 创建虚拟模型和数据
        dummy_model = torch.nn.Linear(10, 1)
        dummy_model_dict = dummy_model.state_dict()
        
        # 模拟保存检查点
        early_stopping.save_checkpoint(0.5, dummy_model_dict, dummy_model, 1, test_save_dir)
        
        # 检查是否设置了best_model_path
        if early_stopping.best_model_path is not None:
            print(f"  ✓ best_model_path set: {early_stopping.best_model_path}")
            
            # 检查文件是否存在
            if os.path.exists(early_stopping.best_model_path):
                print("  ✓ Model file saved successfully")
                # 清理测试文件
                os.remove(early_stopping.best_model_path)
                os.rmdir(test_save_dir)
                return True
            else:
                print("  ✗ Model file not found")
                return False
        else:
            print("  ✗ best_model_path not set")
            return False
        
    except Exception as e:
        print(f"  ✗ EarlyStopping test failed: {e}")
        return False

def test_training_functions():
    """测试训练函数"""
    print("Testing training functions...")
    
    try:
        from main_transformer_fft import train_transformer_model, train_fft_model
        from data.get_inflow_only_dataloader import get_inflow_only_dataloader
        
        # 加载少量数据进行测试
        train_loader, val_loader, _, _, _ = get_inflow_only_dataloader(
            time_interval=15, time_lag=10, tg_in_one_day=72, 
            forecast_day_number=5, pre_len=1, batch_size=4
        )
        
        device = torch.device('cpu')  # 使用CPU进行快速测试
        
        # 创建测试保存目录
        test_transformer_dir = './test_transformer'
        test_fft_dir = './test_fft'
        os.makedirs(test_transformer_dir, exist_ok=True)
        os.makedirs(test_fft_dir, exist_ok=True)
        
        print("  Testing Transformer training function...")
        # 测试Transformer训练函数（只训练1个epoch）
        transformer_model, transformer_path = train_transformer_model(
            device, 10, 1, 276, train_loader, val_loader,
            max_epochs=1, lr=0.001, save_dir=test_transformer_dir
        )
        
        if transformer_path is not None and os.path.exists(transformer_path):
            print(f"    ✓ Transformer training completed: {transformer_path}")
            os.remove(transformer_path)
        else:
            print("    ✗ Transformer training failed")
            return False
        
        print("  Testing FFT training function...")
        # 测试FFT训练函数（只训练1个epoch）
        fft_model, fft_path = train_fft_model(
            device, 10, 276, train_loader, val_loader,
            max_epochs=1, lr=0.001, save_dir=test_fft_dir
        )
        
        if fft_path is not None and os.path.exists(fft_path):
            print(f"    ✓ FFT training completed: {fft_path}")
            os.remove(fft_path)
        else:
            print("    ✗ FFT training failed")
            return False
        
        # 清理测试目录
        os.rmdir(test_transformer_dir)
        os.rmdir(test_fft_dir)
        
        return True
        
    except Exception as e:
        print(f"  ✗ Training functions test failed: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("Testing Fix for EarlyStopping AttributeError")
    print("=" * 60)
    
    tests = [
        ("EarlyStopping Fix", test_earlystopping_fix),
        ("Training Functions", test_training_functions),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 40)
        result = test_func()
        results.append((test_name, result))
    
    # 总结
    print("\n" + "=" * 60)
    print("Test Results Summary:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"  {test_name:<20}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The fix is successful.")
        print("You can now run 'python main_transformer_fft.py' without errors.")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
