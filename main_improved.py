import numpy as np
import os, time, torch
from torch import nn
from torch.utils.tensorboard import SummaryWriter
from utils.utils import GetLaplacian
from model.main_model import Model
import matplotlib.pyplot as plt
from utils.metrics import Metrics, Metrics_1d
from data.get_dataloader import get_inflow_dataloader, get_outflow_dataloader
from utils.earlystopping import EarlyStopping

device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# 改进的超参数
epoch_num = 500
lr = 0.0005  # 降低学习率
time_interval = 15
time_lag = 10
tg_in_one_day = 72
forecast_day_number = 5
pre_len = 1
batch_size = 32
station_num = 276
model_type = 'improved'
TIMESTAMP = str(time.strftime("%Y_%m_%d_%H_%M_%S"))
save_dir = './save_model/' + model_type + '_' + TIMESTAMP
if not os.path.exists(save_dir):
	os.makedirs(save_dir)

# 加载数据
inflow_data_loader_train, inflow_data_loader_val, inflow_data_loader_test, max_inflow, min_inflow = \
	get_inflow_dataloader(time_interval=time_interval, time_lag=time_lag, tg_in_one_day=tg_in_one_day,
						  forecast_day_number=forecast_day_number, pre_len=pre_len, batch_size=batch_size)
outflow_data_loader_train, outflow_data_loader_val, outflow_data_loader_test, max_outflow, min_outflow = \
	get_outflow_dataloader(time_interval=time_interval, time_lag=time_lag, tg_in_one_day=tg_in_one_day,
						   forecast_day_number=forecast_day_number, pre_len=pre_len, batch_size=batch_size)

print(f"Data normalization - max_inflow: {max_inflow}, min_inflow: {min_inflow}")
print(f"Data normalization - max_outflow: {max_outflow}, min_outflow: {min_outflow}")

# 获取归一化的邻接矩阵
adjacency = np.loadtxt('./data/adjacency.csv', delimiter=",")
adjacency = torch.tensor(GetLaplacian(adjacency).get_normalized_adj(station_num)).type(torch.float32).to(device)

global_start_time = time.time()
writer = SummaryWriter(log_dir=f'./logs/{model_type}_{TIMESTAMP}')

# 权重初始化函数
def weights_init(m):
	classname = m.__class__.__name__
	if classname.find('Conv2d') != -1:
		nn.init.xavier_normal_(m.weight.data)
		nn.init.constant_(m.bias.data, 0.0)
	elif classname.find('Linear') != -1:
		nn.init.xavier_normal_(m.weight.data)
		nn.init.constant_(m.bias.data, 0.0)

# 创建模型
model = Model(time_lag, pre_len, station_num, device)
print("Model architecture:")
print(model)
model.apply(weights_init)

if torch.cuda.is_available():
	model.cuda()

model = model.to(device)

# 优化器和学习率调度器
optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-5)
scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=20, verbose=True)

# 损失函数
mse = torch.nn.MSELoss().to(device)

# 早停机制
early_stopping = EarlyStopping(patience=50, verbose=True)

print("Starting training...")
temp_time = time.time()

for epoch in range(epoch_num):
	# 训练阶段
	model.train()
	train_loss = 0

	for inflow_tr, outflow_tr in zip(enumerate(inflow_data_loader_train), enumerate(outflow_data_loader_train)):
		i_batch, (train_inflow_X, train_inflow_Y) = inflow_tr
		i_batch, (train_outflow_X, train_outflow_Y) = outflow_tr

		train_inflow_X = train_inflow_X.type(torch.float32).to(device)
		train_inflow_Y = train_inflow_Y.type(torch.float32).to(device)
		train_outflow_X = train_outflow_X.type(torch.float32).to(device)
		train_outflow_Y = train_outflow_Y.type(torch.float32).to(device)

		# 前向传播
		target = model(train_inflow_X, train_outflow_X, adjacency)
		loss = mse(target, train_inflow_Y)

		# 反向传播
		optimizer.zero_grad()
		loss.backward()

		# 梯度裁剪
		torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

		optimizer.step()
		train_loss += loss.item()

	# 验证阶段
	with torch.no_grad():
		model.eval()
		val_loss = 0

		for inflow_val, outflow_val in zip(enumerate(inflow_data_loader_val), enumerate(outflow_data_loader_val)):
			i_batch, (val_inflow_X, val_inflow_Y) = inflow_val
			i_batch, (val_outflow_X, val_outflow_Y) = outflow_val

			val_inflow_X = val_inflow_X.type(torch.float32).to(device)
			val_inflow_Y = val_inflow_Y.type(torch.float32).to(device)
			val_outflow_X = val_outflow_X.type(torch.float32).to(device)
			val_outflow_Y = val_outflow_Y.type(torch.float32).to(device)

			target = model(val_inflow_X, val_outflow_X, adjacency)
			loss = mse(target, val_inflow_Y)
			val_loss += loss.item()

	# 计算平均损失
	avg_train_loss = train_loss / len(inflow_data_loader_train)
	avg_val_loss = val_loss / len(inflow_data_loader_val)

	# 学习率调度
	scheduler.step(avg_val_loss)

	# 记录到tensorboard
	writer.add_scalar("Loss/Train", avg_train_loss, epoch)
	writer.add_scalar("Loss/Validation", avg_val_loss, epoch)
	writer.add_scalar("Learning_Rate", optimizer.param_groups[0]['lr'], epoch)

	print(f'Epoch {epoch:3d}: Train Loss {avg_train_loss:.6f}, Val Loss {avg_val_loss:.6f}, LR {optimizer.param_groups[0]["lr"]:.6f}')

	# 早停检查
	if epoch > 10:  # 给模型一些时间来学习
		model_dict = model.state_dict()
		early_stopping(avg_val_loss, model_dict, model, epoch, save_dir)
		if early_stopping.early_stop:
			print("Early Stopping triggered!")
			break

	# 每20个epoch打印训练时间
	if epoch % 20 == 0 and epoch > 0:
		elapsed_time = time.time() - temp_time
		print(f"Time for last 20 epochs: {elapsed_time:.2f} seconds")
		temp_time = time.time()

global_end_time = time.time() - global_start_time
print(f"Total training time: {global_end_time:.2f} seconds")

# 保存训练时间
Train_time_ALL = [global_end_time]
if not os.path.exists('result'):
	os.makedirs('result')
np.savetxt(f'result/improved_lr_{lr}_batch_size_{batch_size}_Train_time_ALL.txt', Train_time_ALL)

writer.close()
print("Training completed!")
