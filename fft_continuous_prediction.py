#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
FFT-based Continuous Peak Flow Prediction for Subway Traffic
基于FFT的地铁客流连续高峰预测系统 - 多周期连续展示版本

Author: 008 - AI Algorithm Engineer for Subway Flow Prediction
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ContinuousFFTPeakFlowDataset:
    """连续FFT高峰客流数据集处理类"""
    
    def __init__(self, data_path='./data/in_15min.csv'):
        self.data_path = data_path
        self.raw_data = None
        self.peak_sequences = None
        self.peak_times = None
        self.continuous_sequences = None
        self.station_num = 276
        self.time_steps_per_day = 72
        self.total_days = 25
        self.sequence_length = 10  # 前4个+峰值+后5个
        
    def load_and_process_data(self):
        """加载并处理原始数据"""
        print("正在加载原始数据...")
        self.raw_data = np.loadtxt(self.data_path, delimiter=",")
        print(f"原始数据形状: {self.raw_data.shape}")
        
        # 重塑数据为 (stations, time_steps)
        if self.raw_data.shape[0] == self.station_num:
            self.raw_data = self.raw_data  # 已经是正确形状
        else:
            self.raw_data = self.raw_data.T  # 转置
            
        print(f"处理后数据形状: {self.raw_data.shape}")
        
    def find_peak_sequences(self):
        """找出每个车站第一天的高峰时刻及构建序列"""
        print("正在识别各车站第一天的高峰时刻...")
        
        self.peak_times = []
        self.peak_sequences = []
        
        for station_idx in range(self.station_num):
            # 获取第一天的数据
            first_day_data = self.raw_data[station_idx, :self.time_steps_per_day]
            
            # 找到最高客流量的时刻
            peak_time = np.argmax(first_day_data)
            self.peak_times.append(peak_time)
            
            # 构建10个时间步的序列（前4个+峰值+后5个）
            start_idx = max(0, peak_time - 4)
            end_idx = min(self.time_steps_per_day, peak_time + 6)
            
            # 如果序列不足10个，进行填充
            sequence = first_day_data[start_idx:end_idx]
            if len(sequence) < self.sequence_length:
                if start_idx == 0:
                    # 前面不足，后面填充
                    sequence = np.pad(sequence, (0, self.sequence_length - len(sequence)), 'edge')
                else:
                    # 后面不足，前面填充
                    sequence = np.pad(sequence, (self.sequence_length - len(sequence), 0), 'edge')
            
            self.peak_sequences.append(sequence)
            
        self.peak_sequences = np.array(self.peak_sequences)
        print(f"高峰序列形状: {self.peak_sequences.shape}")
        print(f"前5个车站的高峰时刻: {self.peak_times[:5]}")
        
    def create_multi_day_dataset(self):
        """创建多天数据集 - 每天独立的10时间步序列"""
        print("正在构建多天数据集...")

        # 为每个车站收集所有天数相同时刻的数据
        multi_day_data = []

        for station_idx in range(self.station_num):
            station_data = []
            peak_time = self.peak_times[station_idx]

            # 收集每一天相同时刻的10个时间步序列
            for day in range(self.total_days):
                day_start = day * self.time_steps_per_day

                # 计算当天对应的序列位置
                start_idx = max(0, day_start + peak_time - 4)
                end_idx = min(self.raw_data.shape[1], day_start + peak_time + 6)

                # 获取序列
                if end_idx - start_idx >= self.sequence_length:
                    sequence = self.raw_data[station_idx, start_idx:start_idx + self.sequence_length]
                else:
                    # 处理边界情况
                    sequence = self.raw_data[station_idx, start_idx:end_idx]
                    if len(sequence) < self.sequence_length:
                        sequence = np.pad(sequence, (0, self.sequence_length - len(sequence)), 'edge')

                station_data.append(sequence)

            multi_day_data.append(station_data)

        self.multi_day_data = np.array(multi_day_data)  # (stations, days, sequence_length)
        print(f"多天数据集形状: {self.multi_day_data.shape}")
        
    def save_dataset(self, save_path='./data/continuous_peak_flow_dataset.csv'):
        """保存新数据集"""
        print(f"正在保存数据集到 {save_path}...")

        # 重塑数据为2D格式保存
        # 每行代表一个车站的所有天数数据
        reshaped_data = self.multi_day_data.reshape(self.station_num, -1)

        # 保存为CSV
        np.savetxt(save_path, reshaped_data, delimiter=',', fmt='%.6f')
        print(f"数据集已保存，形状: {reshaped_data.shape}")

        # 保存高峰时刻信息
        peak_info_path = save_path.replace('.csv', '_peak_times.csv')
        np.savetxt(peak_info_path, self.peak_times, delimiter=',', fmt='%d')
        print(f"高峰时刻信息已保存到 {peak_info_path}")

    def split_dataset(self, train_ratio=0.7, val_ratio=0.2):
        """划分训练集、验证集、测试集"""
        print("正在划分数据集...")

        total_samples = self.total_days
        train_size = int(total_samples * train_ratio)
        val_size = int(total_samples * val_ratio)

        # 划分数据
        train_data = self.multi_day_data[:, :train_size, :]
        val_data = self.multi_day_data[:, train_size:train_size + val_size, :]
        test_data = self.multi_day_data[:, train_size + val_size:, :]

        print(f"训练集形状: {train_data.shape}")
        print(f"验证集形状: {val_data.shape}")
        print(f"测试集形状: {test_data.shape}")

        return train_data, val_data, test_data


class FFTPredictor(nn.Module):
    """基于FFT的多层感知机预测器 - 支持多时刻预测"""

    def __init__(self, input_size, hidden_size=64, output_size=10):
        super(FFTPredictor, self).__init__()
        self.mlp = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_size // 2, output_size)  # 输出10个时刻的预测值
        )

    def forward(self, x):
        return self.mlp(x)


class ContinuousFFTFlowPredictor:
    """连续FFT客流预测主类"""
    
    def __init__(self, sequence_length=10):
        self.sequence_length = sequence_length
        self.scaler = MinMaxScaler()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
    def extract_fft_features(self, sequences):
        """提取FFT特征"""
        fft_features = []
        
        for sequence in sequences:
            # 应用FFT
            fft_result = np.fft.fft(sequence)
            
            # 提取幅度和相位
            magnitude = np.abs(fft_result)
            phase = np.angle(fft_result)
            
            # 只保留前一半频率（由于对称性）
            half_len = len(magnitude) // 2
            magnitude = magnitude[:half_len]
            phase = phase[:half_len]
            
            # 合并特征
            features = np.concatenate([magnitude, phase])
            fft_features.append(features)
            
        return np.array(fft_features)
    
    def prepare_training_data(self, train_data, val_data):
        """准备训练数据 - 对每个时刻进行FFT预测"""
        print("正在准备训练数据 - 每个时刻FFT预测...")

        # 重塑数据
        train_sequences = train_data.reshape(-1, self.sequence_length)
        val_sequences = val_data.reshape(-1, self.sequence_length)

        # 数据归一化
        train_sequences_norm = self.scaler.fit_transform(train_sequences)
        val_sequences_norm = self.scaler.transform(val_sequences)

        # 提取FFT特征
        train_fft_features = self.extract_fft_features(train_sequences_norm)
        val_fft_features = self.extract_fft_features(val_sequences_norm)

        # 输入：FFT特征，输出：完整的10个时刻
        X_train = train_fft_features
        y_train = train_sequences_norm  # 预测完整的10个时刻

        X_val = val_fft_features
        y_val = val_sequences_norm

        print(f"训练数据准备完成: X_train shape: {X_train.shape}, y_train shape: {y_train.shape}")

        return X_train, y_train, X_val, y_val
    
    def train_model(self, X_train, y_train, X_val, y_val, epochs=100, batch_size=32, lr=0.001):
        """训练FFT预测模型"""
        print("正在训练FFT预测模型...")
        
        # 转换为PyTorch张量
        X_train_tensor = torch.FloatTensor(X_train).to(self.device)
        y_train_tensor = torch.FloatTensor(y_train).to(self.device)
        X_val_tensor = torch.FloatTensor(X_val).to(self.device)
        y_val_tensor = torch.FloatTensor(y_val).to(self.device)
        
        # 创建模型
        input_size = X_train.shape[1]
        output_size = y_train.shape[1]  # 10个时刻
        self.model = FFTPredictor(input_size, output_size=output_size).to(self.device)
        
        # 优化器和损失函数
        optimizer = optim.Adam(self.model.parameters(), lr=lr)
        criterion = nn.MSELoss()
        
        # 训练循环
        train_losses = []
        val_losses = []
        
        for epoch in range(epochs):
            # 训练阶段
            self.model.train()
            train_loss = 0
            
            for i in range(0, len(X_train_tensor), batch_size):
                batch_X = X_train_tensor[i:i+batch_size]
                batch_y = y_train_tensor[i:i+batch_size]

                optimizer.zero_grad()
                outputs = self.model(batch_X)  # 输出形状: (batch_size, 10)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()

                train_loss += loss.item()

            # 验证阶段
            self.model.eval()
            with torch.no_grad():
                val_outputs = self.model(X_val_tensor)  # 输出形状: (val_size, 10)
                val_loss = criterion(val_outputs, y_val_tensor).item()
            
            train_losses.append(train_loss / (len(X_train_tensor) // batch_size))
            val_losses.append(val_loss)
            
            if epoch % 20 == 0:
                print(f'Epoch {epoch}, Train Loss: {train_losses[-1]:.6f}, Val Loss: {val_losses[-1]:.6f}')
        
        return train_losses, val_losses

    def predict_and_evaluate(self, test_data, station_indices=None):
        """预测并评估模型性能 - 对每个时刻进行FFT预测"""
        print("正在进行预测和评估 - 每个时刻FFT预测...")

        num_stations = test_data.shape[0]
        num_test_days = test_data.shape[1]

        # 存储所有车站的预测结果
        all_predictions = []  # 形状: (stations, days, 10)
        all_true_values = []  # 形状: (stations, days, 10)

        for station_idx in range(num_stations):
            station_predictions = []
            station_true_values = []

            # 对每个测试周期进行预测
            for day in range(num_test_days):
                sequence = test_data[station_idx, day, :]  # 10个时间步

                # 归一化
                sequence_norm = self.scaler.transform(sequence.reshape(1, -1))[0]

                # 提取FFT特征
                fft_features = self.extract_fft_features([sequence_norm])[0]

                # 预测完整的10个时间步
                self.model.eval()
                with torch.no_grad():
                    X_tensor = torch.FloatTensor(fft_features).unsqueeze(0).to(self.device)
                    pred_norm = self.model(X_tensor).cpu().numpy()[0]  # 形状: (10,)

                # 反归一化预测结果
                pred_full = self.scaler.inverse_transform(pred_norm.reshape(1, -1))
                predicted_sequence = pred_full[0]  # 完整的10个时间步预测

                station_predictions.append(predicted_sequence)
                station_true_values.append(sequence)  # 真实的10个时间步

            all_predictions.append(station_predictions)
            all_true_values.append(station_true_values)

        all_predictions = np.array(all_predictions)  # 形状: (stations, days, 10)
        all_true_values = np.array(all_true_values)  # 形状: (stations, days, 10)

        # 计算整体评估指标
        rmse = np.sqrt(mean_squared_error(all_true_values.flatten(), all_predictions.flatten()))
        mae = mean_absolute_error(all_true_values.flatten(), all_predictions.flatten())
        wmape = self.calculate_wmape(all_true_values.flatten(), all_predictions.flatten())
        r2 = r2_score(all_true_values.flatten(), all_predictions.flatten())

        print(f"整体评估结果:")
        print(f"RMSE: {rmse:.4f}")
        print(f"MAE: {mae:.4f}")
        print(f"WMAPE: {wmape:.4f}")
        print(f"R2: {r2:.4f}")

        # 计算各车站的详细指标
        station_metrics = self.calculate_station_metrics(all_predictions, all_true_values)

        # 保存预测结果和详细指标
        self.save_predictions(all_predictions, all_true_values, test_data)
        self.save_detailed_metrics(station_metrics, rmse, mae, wmape, r2)

        # 绘制连续n*10对比图
        self.plot_continuous_n_cycles_comparisons(all_predictions, all_true_values, test_data, station_indices)

        # 绘制连续5*10对比图（横坐标0-50）
        self.plot_continuous_5_cycles_comparisons(all_predictions, all_true_values, test_data, station_indices)

        return all_predictions, all_true_values, rmse, mae, wmape, r2, station_metrics

    def calculate_wmape(self, y_true, y_pred):
        """计算加权平均绝对百分比误差"""
        mask = y_true > 0
        if np.sum(mask) == 0:
            return 0
        return np.sum(np.abs(y_pred[mask] - y_true[mask])) / np.sum(y_true[mask])

    def calculate_station_metrics(self, predictions, true_values):
        """计算各车站的详细评估指标"""
        print("正在计算各车站的详细评估指标...")

        num_stations = predictions.shape[0]
        station_metrics = []

        for station_idx in range(num_stations):
            station_pred = predictions[station_idx].flatten()
            station_true = true_values[station_idx].flatten()

            # 计算各项指标
            rmse = np.sqrt(mean_squared_error(station_true, station_pred))
            mae = mean_absolute_error(station_true, station_pred)
            wmape = self.calculate_wmape(station_true, station_pred)
            r2 = r2_score(station_true, station_pred)

            station_metrics.append({
                'station_id': station_idx,
                'rmse': rmse,
                'mae': mae,
                'wmape': wmape,
                'r2': r2
            })

        return station_metrics

    def save_predictions(self, predictions, true_values, test_data):
        """保存预测结果到txt文件 - 每个时刻的预测"""
        if not os.path.exists('result'):
            os.makedirs('result')

        num_stations = predictions.shape[0]
        num_test_days = predictions.shape[1]

        # 保存预测结果和时刻信息
        with open('result/fft_continuous_predictions.txt', 'w', encoding='utf-8') as f:
            f.write("FFT每个时刻预测结果 - 所有276个车站\n")
            f.write("=" * 100 + "\n")
            f.write("格式: 车站ID | 高峰时刻 | 各周期完整10时间步预测值 | 各周期完整10时间步真实值\n")
            f.write("-" * 120 + "\n")

            # 加载高峰时刻信息
            peak_times = np.loadtxt('data/continuous_peak_flow_dataset_peak_times.csv', delimiter=',', dtype=int)

            for station_idx in range(num_stations):
                peak_time = peak_times[station_idx]
                hour = peak_time // 4
                minute = (peak_time % 4) * 15

                f.write(f"车站{station_idx:3d} | {hour:02d}:{minute:02d}\n")

                # 写入各周期的完整预测序列
                for day in range(num_test_days):
                    f.write(f"  周期{day+1} 预测: ")
                    f.write(" ".join([f"{pred:7.1f}" for pred in predictions[station_idx, day]]))
                    f.write("\n")

                    f.write(f"  周期{day+1} 真实: ")
                    f.write(" ".join([f"{true:7.1f}" for true in true_values[station_idx, day]]))
                    f.write("\n")

                # 计算该车站的RMSE
                station_rmse = np.sqrt(np.mean((predictions[station_idx] - true_values[station_idx]) ** 2))
                f.write(f"  车站RMSE: {station_rmse:.2f}\n")
                f.write("\n")

        print("预测结果已保存到 result/fft_continuous_predictions.txt")

    def save_detailed_metrics(self, station_metrics, overall_rmse, overall_mae, overall_wmape, overall_r2):
        """保存详细的评估指标"""
        if not os.path.exists('result'):
            os.makedirs('result')

        # 保存所有车站的详细指标
        with open('result/fft_detailed_metrics.txt', 'w', encoding='utf-8') as f:
            f.write("FFT每个时刻预测 - 详细评估指标报告\n")
            f.write("=" * 80 + "\n\n")

            # 整体指标
            f.write("整体评估指标:\n")
            f.write("-" * 40 + "\n")
            f.write(f"RMSE: {overall_rmse:.4f}\n")
            f.write(f"MAE: {overall_mae:.4f}\n")
            f.write(f"WMAPE: {overall_wmape:.4f}\n")
            f.write(f"R2: {overall_r2:.4f}\n\n")

            # 各车站指标统计
            rmses = [m['rmse'] for m in station_metrics]
            maes = [m['mae'] for m in station_metrics]
            wmapes = [m['wmape'] for m in station_metrics]
            r2s = [m['r2'] for m in station_metrics]

            f.write("各车站指标统计:\n")
            f.write("-" * 40 + "\n")
            f.write(f"RMSE - 平均: {np.mean(rmses):.2f}, 标准差: {np.std(rmses):.2f}, 最小: {np.min(rmses):.2f}, 最大: {np.max(rmses):.2f}\n")
            f.write(f"MAE  - 平均: {np.mean(maes):.2f}, 标准差: {np.std(maes):.2f}, 最小: {np.min(maes):.2f}, 最大: {np.max(maes):.2f}\n")
            f.write(f"WMAPE- 平均: {np.mean(wmapes):.4f}, 标准差: {np.std(wmapes):.4f}, 最小: {np.min(wmapes):.4f}, 最大: {np.max(wmapes):.4f}\n")
            f.write(f"R2   - 平均: {np.mean(r2s):.4f}, 标准差: {np.std(r2s):.4f}, 最小: {np.min(r2s):.4f}, 最大: {np.max(r2s):.4f}\n\n")

            # 表现最好和最差的车站
            best_rmse_idx = np.argmin(rmses)
            worst_rmse_idx = np.argmax(rmses)
            best_r2_idx = np.argmax(r2s)
            worst_r2_idx = np.argmin(r2s)

            f.write("表现最好的车站:\n")
            f.write("-" * 40 + "\n")
            f.write(f"RMSE最小 - 车站{best_rmse_idx}: RMSE={rmses[best_rmse_idx]:.2f}, MAE={maes[best_rmse_idx]:.2f}, WMAPE={wmapes[best_rmse_idx]:.4f}, R2={r2s[best_rmse_idx]:.4f}\n")
            f.write(f"R2最大   - 车站{best_r2_idx}: RMSE={rmses[best_r2_idx]:.2f}, MAE={maes[best_r2_idx]:.2f}, WMAPE={wmapes[best_r2_idx]:.4f}, R2={r2s[best_r2_idx]:.4f}\n\n")

            f.write("表现最差的车站:\n")
            f.write("-" * 40 + "\n")
            f.write(f"RMSE最大 - 车站{worst_rmse_idx}: RMSE={rmses[worst_rmse_idx]:.2f}, MAE={maes[worst_rmse_idx]:.2f}, WMAPE={wmapes[worst_rmse_idx]:.4f}, R2={r2s[worst_rmse_idx]:.4f}\n")
            f.write(f"R2最小   - 车站{worst_r2_idx}: RMSE={rmses[worst_r2_idx]:.2f}, MAE={maes[worst_r2_idx]:.2f}, WMAPE={wmapes[worst_r2_idx]:.4f}, R2={r2s[worst_r2_idx]:.4f}\n\n")

            # 所有车站的详细指标
            f.write("所有车站详细指标:\n")
            f.write("-" * 80 + "\n")
            f.write("车站ID | RMSE     | MAE      | WMAPE    | R2       \n")
            f.write("-" * 80 + "\n")

            for metrics in station_metrics:
                f.write(f"{metrics['station_id']:6d} | {metrics['rmse']:8.2f} | {metrics['mae']:8.2f} | {metrics['wmape']:8.4f} | {metrics['r2']:8.4f}\n")

        # 保存6个代表性车站的指标
        representative_stations = [4, 18, 30, 60, 94, 232]
        with open('result/fft_representative_stations_metrics.txt', 'w', encoding='utf-8') as f:
            f.write("FFT预测 - 6个代表性车站详细指标\n")
            f.write("=" * 60 + "\n\n")

            # 加载高峰时刻信息
            peak_times = np.loadtxt('data/continuous_peak_flow_dataset_peak_times.csv', delimiter=',', dtype=int)

            for station_id in representative_stations:
                if station_id < len(station_metrics):
                    metrics = station_metrics[station_id]
                    peak_time = peak_times[station_id]
                    hour = peak_time // 4
                    minute = (peak_time % 4) * 15

                    f.write(f"车站 {station_id} (高峰时刻: {hour:02d}:{minute:02d}):\n")
                    f.write("-" * 40 + "\n")
                    f.write(f"RMSE:  {metrics['rmse']:.4f}\n")
                    f.write(f"MAE:   {metrics['mae']:.4f}\n")
                    f.write(f"WMAPE: {metrics['wmape']:.4f}\n")
                    f.write(f"R2:    {metrics['r2']:.4f}\n\n")

        print("详细评估指标已保存到:")
        print("- result/fft_detailed_metrics.txt (所有车站)")
        print("- result/fft_representative_stations_metrics.txt (6个代表性车站)")

    def plot_continuous_n_cycles_comparisons(self, predictions, true_values, test_data, station_indices, num_stations=6):
        """绘制连续n*10周期对比曲线图 - 展示完整的10时间步序列"""
        if station_indices is None:
            # 选择客流量较大的几个车站
            avg_flows = np.mean(true_values, axis=1)
            station_indices = np.argsort(avg_flows)[-num_stations:]
        else:
            station_indices = station_indices[:num_stations]

        fig, axes = plt.subplots(2, 3, figsize=(24, 12))
        axes = axes.flatten()

        # 加载高峰时刻信息
        peak_times = np.loadtxt('data/continuous_peak_flow_dataset_peak_times.csv', delimiter=',', dtype=int)

        num_test_days = test_data.shape[1]

        for i, station_idx in enumerate(station_indices):
            if i >= len(axes):
                break

            ax = axes[i]

            # 构建连续的n*10时间步序列
            continuous_true = []
            continuous_pred = []

            for day in range(num_test_days):
                # 真实值：完整的10时间步序列
                true_sequence = true_values[station_idx, day, :]
                continuous_true.extend(true_sequence)

                # 预测值：完整的10时间步预测序列
                pred_sequence = predictions[station_idx, day, :]
                continuous_pred.extend(pred_sequence)

            # 创建横坐标（n*10个时间步）
            x_positions = range(len(continuous_true))

            # 绘制连续曲线
            ax.plot(x_positions, continuous_true, 'b-', linewidth=2, marker='o', markersize=3,
                   label='真实值', alpha=0.8)
            ax.plot(x_positions, continuous_pred, 'r--', linewidth=2, marker='s', markersize=3,
                   label='FFT预测值', alpha=0.8)

            # 标记每个周期的分界线
            for day in range(num_test_days):
                cycle_start = day * 10

                # 周期分界线
                if day > 0:
                    ax.axvline(x=cycle_start, color='gray', linestyle=':', alpha=0.5, linewidth=1)

                # 周期标签
                ax.text(cycle_start + 5, ax.get_ylim()[1] * 0.95, f'周期{day+1}',
                       ha='center', fontsize=9,
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='lightblue', alpha=0.7))

            # 设置图表属性
            peak_time = peak_times[station_idx]
            hour = peak_time // 4
            minute = (peak_time % 4) * 15

            ax.set_title(f'车站 {station_idx} - 高峰时刻 {hour:02d}:{minute:02d}\n连续{num_test_days}周期预测对比 (横坐标: {num_test_days}×10={len(continuous_true)}时间步)',
                        fontsize=12, fontweight='bold')
            ax.set_xlabel(f'连续时间步 (共{len(continuous_true)}步)', fontsize=11)
            ax.set_ylabel('客流量', fontsize=11)
            ax.legend(fontsize=10)
            ax.grid(True, alpha=0.3)

            # 计算该车站的RMSE
            station_rmse = np.sqrt(np.mean((predictions[station_idx] - true_values[station_idx]) ** 2))
            ax.text(0.02, 0.02, f'RMSE: {station_rmse:.2f}', transform=ax.transAxes,
                   bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
                   fontsize=10, fontweight='bold')

            # 设置x轴刻度（显示周期边界）
            cycle_ticks = []
            cycle_labels = []
            for day in range(num_test_days + 1):
                cycle_ticks.append(day * 10)
                cycle_labels.append(f'{day * 10}')

            ax.set_xticks(cycle_ticks)
            ax.set_xticklabels(cycle_labels)

        # 隐藏多余的子图
        for i in range(len(station_indices), len(axes)):
            axes[i].set_visible(False)

        plt.tight_layout()
        plt.savefig('result/fft_continuous_n_cycles_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        print(f"连续{num_test_days}周期每个时刻预测对比图已保存到 result/fft_continuous_n_cycles_comparison.png")

    def plot_continuous_5_cycles_comparisons(self, predictions, true_values, test_data, station_indices, num_stations=6):
        """绘制连续5*10周期对比曲线图 - 横坐标0-50范围"""
        if station_indices is None:
            # 选择客流量较大的几个车站
            avg_flows = np.mean(true_values, axis=1)
            station_indices = np.argsort(avg_flows)[-num_stations:]
        else:
            station_indices = station_indices[:num_stations]

        fig, axes = plt.subplots(2, 3, figsize=(24, 12))
        axes = axes.flatten()

        # 加载高峰时刻信息
        peak_times = np.loadtxt('data/continuous_peak_flow_dataset_peak_times.csv', delimiter=',', dtype=int)

        # 使用5个周期（如果数据不足5个周期，则重复使用现有数据）
        num_test_days = test_data.shape[1]
        target_cycles = 5

        for i, station_idx in enumerate(station_indices):
            if i >= len(axes):
                break

            ax = axes[i]

            # 构建连续的5*10时间步序列
            continuous_true = []
            continuous_pred = []

            for cycle in range(target_cycles):
                # 如果数据不足5个周期，循环使用现有数据
                day_idx = cycle % num_test_days

                # 真实值：完整的10时间步序列
                true_sequence = true_values[station_idx, day_idx, :]
                continuous_true.extend(true_sequence)

                # 预测值：完整的10时间步预测序列
                pred_sequence = predictions[station_idx, day_idx, :]
                continuous_pred.extend(pred_sequence)

            # 创建横坐标（5*10=50个时间步）
            x_positions = range(len(continuous_true))

            # 绘制连续曲线
            ax.plot(x_positions, continuous_true, 'b-', linewidth=2, marker='o', markersize=3,
                   label='真实值', alpha=0.8)
            ax.plot(x_positions, continuous_pred, 'r--', linewidth=2, marker='s', markersize=3,
                   label='FFT预测值', alpha=0.8)

            # 标记每个周期的分界线
            for cycle in range(target_cycles):
                cycle_start = cycle * 10

                # 周期分界线
                if cycle > 0:
                    ax.axvline(x=cycle_start, color='gray', linestyle=':', alpha=0.5, linewidth=1)

                # 周期标签
                ax.text(cycle_start + 5, ax.get_ylim()[1] * 0.95, f'周期{cycle+1}',
                       ha='center', fontsize=9,
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='lightblue', alpha=0.7))

            # 设置图表属性
            peak_time = peak_times[station_idx]
            hour = peak_time // 4
            minute = (peak_time % 4) * 15

            ax.set_title(f'车站 {station_idx} - 高峰时刻 {hour:02d}:{minute:02d}\n连续{target_cycles}周期预测对比 (横坐标: 0-{len(continuous_true)}时间步)',
                        fontsize=12, fontweight='bold')
            ax.set_xlabel(f'连续时间步 (0-{len(continuous_true)-1})', fontsize=11)
            ax.set_ylabel('客流量', fontsize=11)
            ax.legend(fontsize=10)
            ax.grid(True, alpha=0.3)

            # 计算该车站的RMSE（基于实际可用数据）
            station_rmse = np.sqrt(np.mean((predictions[station_idx] - true_values[station_idx]) ** 2))
            ax.text(0.02, 0.02, f'RMSE: {station_rmse:.2f}', transform=ax.transAxes,
                   bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
                   fontsize=10, fontweight='bold')

            # 设置x轴刻度（显示周期边界）
            cycle_ticks = []
            cycle_labels = []
            for cycle in range(target_cycles + 1):
                cycle_ticks.append(cycle * 10)
                cycle_labels.append(f'{cycle * 10}')

            ax.set_xticks(cycle_ticks)
            ax.set_xticklabels(cycle_labels)

        # 隐藏多余的子图
        for i in range(len(station_indices), len(axes)):
            axes[i].set_visible(False)

        plt.tight_layout()
        plt.savefig('result/fft_continuous_5_cycles_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        print(f"连续5周期预测对比图已保存到 result/fft_continuous_5_cycles_comparison.png")


def main():
    """主函数"""
    print("=" * 80)
    print("FFT-based Peak Flow Prediction System")
    print("基于FFT的地铁客流高峰预测系统 - 每个时刻FFT预测，n*10连续展示")
    print("=" * 80)

    # 1. 数据预处理
    dataset = ContinuousFFTPeakFlowDataset()
    dataset.load_and_process_data()
    dataset.find_peak_sequences()
    dataset.create_multi_day_dataset()
    dataset.save_dataset()

    # 2. 数据集划分
    train_data, val_data, test_data = dataset.split_dataset()

    # 3. FFT预测
    predictor = ContinuousFFTFlowPredictor()

    # 准备训练数据
    X_train, y_train, X_val, y_val = predictor.prepare_training_data(train_data, val_data)

    # 训练模型
    train_losses, val_losses = predictor.train_model(X_train, y_train, X_val, y_val, epochs=200)

    # 4. 预测和评估
    # 选择一些典型车站进行展示
    typical_stations = [4, 18, 30, 60, 94, 232]  # 可以根据需要调整
    predictions, true_values, rmse, mae, wmape, r2, station_metrics = predictor.predict_and_evaluate(test_data, typical_stations)

    # 5. 输出最终结果
    print("\n" + "=" * 80)
    print("最终评估结果:")
    print(f"所有车站总体 RMSE: {rmse:.4f}")
    print(f"所有车站总体 MAE: {mae:.4f}")
    print(f"所有车站总体 WMAPE: {wmape:.4f}")
    print(f"所有车站总体 R2: {r2:.4f}")
    print(f"测试周期数: {test_data.shape[1]}")
    print(f"每周期时间步数: 10")
    print(f"连续时间步总数: {test_data.shape[1] * 10}")
    print("=" * 80)

    # 输出6个代表性车站的指标
    print("\n6个代表性车站的详细指标:")
    print("-" * 80)
    print("车站ID | 高峰时刻 | RMSE     | MAE      | WMAPE    | R2       ")
    print("-" * 80)

    # 加载高峰时刻信息
    peak_times = np.loadtxt('data/continuous_peak_flow_dataset_peak_times.csv', delimiter=',', dtype=int)

    for station_id in typical_stations:
        if station_id < len(station_metrics):
            metrics = station_metrics[station_id]
            peak_time = peak_times[station_id]
            hour = peak_time // 4
            minute = (peak_time % 4) * 15
            print(f"{station_id:6d} | {hour:02d}:{minute:02d}     | {metrics['rmse']:8.4f} | {metrics['mae']:8.4f} | {metrics['wmape']:8.4f} | {metrics['r2']:8.4f}")

    print("-" * 80)

    # 保存评估结果
    with open('result/fft_continuous_evaluation_summary.txt', 'w', encoding='utf-8') as f:
        f.write("FFT预测模型评估总结\n")
        f.write("=" * 60 + "\n")
        f.write(f"所有车站总体 RMSE: {rmse:.4f}\n")
        f.write(f"所有车站总体 MAE: {mae:.4f}\n")
        f.write(f"所有车站总体 WMAPE: {wmape:.4f}\n")
        f.write(f"所有车站总体 R2: {r2:.4f}\n")
        f.write(f"测试车站数量: {len(predictions)}\n")
        f.write(f"测试周期数: {test_data.shape[1]}\n")
        f.write(f"每周期时间步数: 10\n")
        f.write(f"连续时间步总数: {test_data.shape[1] * 10}\n")
        f.write(f"预测方式: 对每个时刻进行FFT预测，展示n*10连续对比\n")

    print("评估总结已保存到 result/fft_continuous_evaluation_summary.txt")


if __name__ == "__main__":
    main()
