#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分析图卷积网络(GCN)的实现过程和结构
详细解析GCN层数、前向传播过程等

Author: 008 - AI Algorithm Engineer for Subway Flow Prediction
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import os
import sys

# 设置matplotlib后端
import matplotlib
matplotlib.use('Agg')

# 添加模型路径
sys.path.append('.')
from model.GCN_layers import GraphConvolution

def analyze_gcn_structure():
    """分析GCN的结构配置"""
    print("分析GCN结构配置...")
    
    # 工程中的配置参数
    time_lag = 10  # 输入特征维度
    station_num = 276  # 车站数量
    
    print(f"GCN配置参数:")
    print(f"- 输入特征维度 (in_features): {time_lag}")
    print(f"- 输出特征维度 (out_features): {time_lag}")
    print(f"- 车站数量: {station_num}")
    print(f"- GCN层数: 1层")
    
    return time_lag, station_num

def analyze_gcn_forward_process():
    """分析GCN前向传播过程"""
    print("\n分析GCN前向传播过程...")
    
    time_lag = 10
    station_num = 276
    batch_size = 32
    
    # 创建GCN层
    gcn = GraphConvolution(in_features=time_lag, out_features=time_lag)
    
    print(f"GCN层参数:")
    print(f"- 权重矩阵形状: {gcn.weight.shape}")
    print(f"- 偏置向量形状: {gcn.bias.shape if gcn.bias is not None else 'None'}")
    
    # 模拟输入数据
    torch.manual_seed(42)
    x = torch.randn(batch_size, station_num, time_lag)  # 输入特征
    adj = torch.randn(station_num, station_num)  # 邻接矩阵
    adj = torch.softmax(adj, dim=1)  # 归一化邻接矩阵
    
    print(f"\n输入数据形状:")
    print(f"- 特征矩阵 x: {x.shape}")
    print(f"- 邻接矩阵 adj: {adj.shape}")
    
    # 前向传播
    output = gcn(x, adj)
    
    print(f"\n输出数据形状:")
    print(f"- 输出特征矩阵: {output.shape}")
    
    return x, adj, output, gcn

def demonstrate_gcn_computation_steps():
    """演示GCN计算步骤"""
    print("\n演示GCN计算步骤...")
    
    # 简化示例：3个车站，2个特征
    station_num = 3
    feature_dim = 2
    batch_size = 1
    
    # 创建简化的GCN
    gcn_simple = GraphConvolution(in_features=feature_dim, out_features=feature_dim)
    
    # 设置固定权重便于演示
    with torch.no_grad():
        gcn_simple.weight.data = torch.tensor([[1.0, 0.5], [0.3, 1.0]])
        gcn_simple.bias.data = torch.tensor([0.1, 0.2])
    
    # 创建示例数据
    x_simple = torch.tensor([[[1.0, 2.0],   # 车站0的特征
                             [3.0, 1.0],   # 车站1的特征
                             [2.0, 3.0]]]) # 车站2的特征
    
    adj_simple = torch.tensor([[0.5, 0.3, 0.2],  # 车站0与其他车站的连接
                              [0.4, 0.4, 0.2],  # 车站1与其他车站的连接
                              [0.3, 0.3, 0.4]]) # 车站2与其他车站的连接
    
    print(f"简化示例:")
    print(f"输入特征矩阵 x:")
    print(x_simple.squeeze(0).numpy())
    print(f"\n邻接矩阵 adj:")
    print(adj_simple.numpy())
    print(f"\nGCN权重矩阵:")
    print(gcn_simple.weight.data.numpy())
    print(f"\nGCN偏置向量:")
    print(gcn_simple.bias.data.numpy())
    
    # 手动计算步骤
    print(f"\n计算步骤:")
    
    # 步骤1: 特征变换 X * W
    support = torch.matmul(x_simple, gcn_simple.weight)
    print(f"1. 特征变换 (X * W):")
    print(support.squeeze(0).numpy())
    
    # 步骤2: 图卷积 A * (X * W)
    output_manual = torch.bmm(adj_simple.unsqueeze(0).expand(1, *adj_simple.size()), support)
    print(f"\n2. 图卷积 (A * (X * W)):")
    print(output_manual.squeeze(0).numpy())
    
    # 步骤3: 添加偏置
    output_with_bias = output_manual + gcn_simple.bias
    print(f"\n3. 添加偏置 (A * (X * W) + b):")
    print(output_with_bias.squeeze(0).numpy())
    
    # 使用GCN层计算
    output_gcn = gcn_simple(x_simple, adj_simple)
    print(f"\nGCN层输出:")
    print(output_gcn.squeeze(0).numpy())
    
    # 验证计算一致性
    print(f"\n计算验证:")
    print(f"手动计算与GCN层输出是否一致: {torch.allclose(output_with_bias, output_gcn)}")
    
    return x_simple, adj_simple, output_gcn

def analyze_gcn_usage_in_model():
    """分析GCN在整个模型中的使用"""
    print("\n分析GCN在整个模型中的使用...")
    
    print(f"GCN在模型中的使用模式:")
    print(f"1. 模型中只有1个GCN层")
    print(f"2. 该GCN层被重复使用3次，分别处理:")
    print(f"   - inflow_week: 周期性进站客流")
    print(f"   - inflow_day: 日期性进站客流") 
    print(f"   - inflow_time: 时刻性进站客流")
    print(f"3. 每次使用相同的权重参数")
    print(f"4. 输入输出维度保持不变: {10} -> {10}")
    
    print(f"\n数据流向:")
    print(f"输入数据 -> 分离三个时间段 -> GCN处理 -> 特征融合")
    print(f"[batch, 276, 30] -> 3×[batch, 276, 10] -> 3×GCN -> 3×[batch, 276, 10]")
    
    return

def create_gcn_visualization():
    """创建GCN可视化图表"""
    print("\n创建GCN可视化图表...")
    
    # 确保result目录存在
    if not os.path.exists('result'):
        os.makedirs('result')
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 子图1: GCN层结构示意图
    ax1 = axes[0, 0]
    ax1.text(0.5, 0.8, 'GCN Layer Structure', ha='center', va='center', 
             fontsize=14, fontweight='bold', transform=ax1.transAxes)
    ax1.text(0.5, 0.6, 'Input: [batch, 276, 10]', ha='center', va='center', 
             fontsize=12, transform=ax1.transAxes, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
    ax1.text(0.5, 0.4, 'GCN(in_features=10, out_features=10)', ha='center', va='center', 
             fontsize=12, transform=ax1.transAxes, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen"))
    ax1.text(0.5, 0.2, 'Output: [batch, 276, 10]', ha='center', va='center', 
             fontsize=12, transform=ax1.transAxes, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral"))
    ax1.set_xlim(0, 1)
    ax1.set_ylim(0, 1)
    ax1.axis('off')
    
    # 子图2: GCN计算公式
    ax2 = axes[0, 1]
    ax2.text(0.5, 0.8, 'GCN Computation Formula', ha='center', va='center', 
             fontsize=14, fontweight='bold', transform=ax2.transAxes)
    ax2.text(0.5, 0.6, 'H = A × (X × W) + b', ha='center', va='center', 
             fontsize=16, transform=ax2.transAxes, bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow"))
    ax2.text(0.5, 0.4, 'A: Adjacency Matrix [276×276]', ha='center', va='center', 
             fontsize=10, transform=ax2.transAxes)
    ax2.text(0.5, 0.3, 'X: Feature Matrix [batch×276×10]', ha='center', va='center', 
             fontsize=10, transform=ax2.transAxes)
    ax2.text(0.5, 0.2, 'W: Weight Matrix [10×10]', ha='center', va='center', 
             fontsize=10, transform=ax2.transAxes)
    ax2.text(0.5, 0.1, 'b: Bias Vector [10]', ha='center', va='center', 
             fontsize=10, transform=ax2.transAxes)
    ax2.set_xlim(0, 1)
    ax2.set_ylim(0, 1)
    ax2.axis('off')
    
    # 子图3: 模型中GCN的使用流程
    ax3 = axes[1, 0]
    ax3.text(0.5, 0.9, 'GCN Usage in Model', ha='center', va='center', 
             fontsize=14, fontweight='bold', transform=ax3.transAxes)
    
    # 绘制流程图
    boxes = [
        (0.5, 0.8, 'Input Data\n[batch, 276, 30]'),
        (0.2, 0.6, 'inflow_week\n[batch, 276, 10]'),
        (0.5, 0.6, 'inflow_day\n[batch, 276, 10]'),
        (0.8, 0.6, 'inflow_time\n[batch, 276, 10]'),
        (0.2, 0.4, 'GCN Layer'),
        (0.5, 0.4, 'GCN Layer'),
        (0.8, 0.4, 'GCN Layer'),
        (0.2, 0.2, 'gcn_inflow_week'),
        (0.5, 0.2, 'gcn_inflow_day'),
        (0.8, 0.2, 'gcn_inflow_time'),
        (0.5, 0.05, 'Feature Fusion')
    ]
    
    for x, y, text in boxes:
        ax3.text(x, y, text, ha='center', va='center', fontsize=9, 
                transform=ax3.transAxes, bbox=dict(boxstyle="round,pad=0.2", facecolor="lightgray"))
    
    # 绘制箭头
    arrows = [
        (0.5, 0.75, 0.2, 0.65), (0.5, 0.75, 0.5, 0.65), (0.5, 0.75, 0.8, 0.65),
        (0.2, 0.55, 0.2, 0.45), (0.5, 0.55, 0.5, 0.45), (0.8, 0.55, 0.8, 0.45),
        (0.2, 0.35, 0.2, 0.25), (0.5, 0.35, 0.5, 0.25), (0.8, 0.35, 0.8, 0.25),
        (0.2, 0.15, 0.5, 0.1), (0.5, 0.15, 0.5, 0.1), (0.8, 0.15, 0.5, 0.1)
    ]
    
    for x1, y1, x2, y2 in arrows:
        ax3.annotate('', xy=(x2, y2), xytext=(x1, y1), 
                    arrowprops=dict(arrowstyle='->', color='blue', lw=1),
                    xycoords='axes fraction', textcoords='axes fraction')
    
    ax3.set_xlim(0, 1)
    ax3.set_ylim(0, 1)
    ax3.axis('off')
    
    # 子图4: GCN参数统计
    ax4 = axes[1, 1]
    
    # 创建参数统计
    categories = ['Weight\nParameters', 'Bias\nParameters', 'Total\nParameters']
    values = [10*10, 10, 10*10+10]  # 权重矩阵 + 偏置向量
    colors = ['skyblue', 'lightgreen', 'lightcoral']
    
    bars = ax4.bar(categories, values, color=colors, alpha=0.7)
    ax4.set_title('GCN Layer Parameters', fontsize=14, fontweight='bold')
    ax4.set_ylabel('Number of Parameters', fontsize=12)
    
    # 添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{value}', ha='center', va='bottom', fontsize=11, fontweight='bold')
    
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('result/gcn_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("GCN分析图已保存到 result/gcn_analysis.png")

def save_detailed_analysis():
    """保存详细的GCN分析"""
    print("保存详细的GCN分析...")
    
    if not os.path.exists('result'):
        os.makedirs('result')
    
    with open('result/gcn_implementation_analysis.txt', 'w', encoding='utf-8') as f:
        f.write("图卷积网络(GCN)实现过程详细分析\n")
        f.write("=" * 80 + "\n\n")
        
        f.write("1. GCN层数配置:\n")
        f.write("-" * 40 + "\n")
        f.write("- GCN层数: 1层\n")
        f.write("- 输入特征维度: 10 (time_lag)\n")
        f.write("- 输出特征维度: 10 (保持不变)\n")
        f.write("- 车站数量: 276\n")
        f.write("- 邻接矩阵大小: 276×276\n\n")
        
        f.write("2. GCN实现过程:\n")
        f.write("-" * 40 + "\n")
        f.write("步骤1: 特征变换\n")
        f.write("  support = X × W\n")
        f.write("  其中 X: [batch, 276, 10], W: [10, 10]\n")
        f.write("  结果: support [batch, 276, 10]\n\n")
        
        f.write("步骤2: 图卷积操作\n")
        f.write("  output = A × support\n")
        f.write("  其中 A: [276, 276] (归一化邻接矩阵)\n")
        f.write("  结果: output [batch, 276, 10]\n\n")
        
        f.write("步骤3: 添加偏置\n")
        f.write("  final_output = output + bias\n")
        f.write("  其中 bias: [10]\n")
        f.write("  结果: final_output [batch, 276, 10]\n\n")
        
        f.write("3. GCN在模型中的使用:\n")
        f.write("-" * 40 + "\n")
        f.write("- 同一个GCN层被重复使用3次\n")
        f.write("- 分别处理三个时间维度的进站客流:\n")
        f.write("  * inflow_week: 周期性特征\n")
        f.write("  * inflow_day: 日期性特征\n")
        f.write("  * inflow_time: 时刻性特征\n")
        f.write("- 每次使用相同的权重参数\n")
        f.write("- 共享参数减少了模型复杂度\n\n")
        
        f.write("4. 数据流向:\n")
        f.write("-" * 40 + "\n")
        f.write("输入: [batch, 276, 30] (三个时间段拼接)\n")
        f.write("  ↓ 分离\n")
        f.write("inflow_week: [batch, 276, 10]\n")
        f.write("inflow_day:  [batch, 276, 10]\n")
        f.write("inflow_time: [batch, 276, 10]\n")
        f.write("  ↓ GCN处理\n")
        f.write("gcn_inflow_week: [batch, 276, 10]\n")
        f.write("gcn_inflow_day:  [batch, 276, 10]\n")
        f.write("gcn_inflow_time: [batch, 276, 10]\n")
        f.write("  ↓ 特征融合\n")
        f.write("融合特征: [batch, 276, 90] (与Transformer特征拼接)\n\n")
        
        f.write("5. GCN参数统计:\n")
        f.write("-" * 40 + "\n")
        f.write("- 权重矩阵参数: 10 × 10 = 100个\n")
        f.write("- 偏置向量参数: 10个\n")
        f.write("- 总参数数量: 110个\n")
        f.write("- 参数共享: 同一GCN层处理3个时间维度\n\n")
        
        f.write("6. GCN的作用:\n")
        f.write("-" * 40 + "\n")
        f.write("- 捕捉车站间的空间依赖关系\n")
        f.write("- 利用地铁网络的拓扑结构\n")
        f.write("- 聚合邻近车站的特征信息\n")
        f.write("- 为每个车站提供空间上下文信息\n")
        f.write("- 与Transformer的时间建模形成互补\n\n")
        
        f.write("7. 技术特点:\n")
        f.write("-" * 40 + "\n")
        f.write("- 简化设计: 只使用1层GCN\n")
        f.write("- 参数共享: 减少模型复杂度\n")
        f.write("- 维度保持: 输入输出维度相同\n")
        f.write("- 高效计算: 矩阵运算优化\n")
        f.write("- 空间建模: 专注于空间依赖关系\n")
    
    print("详细分析已保存到 result/gcn_implementation_analysis.txt")

def main():
    """主函数"""
    print("=" * 80)
    print("图卷积网络(GCN)实现过程分析")
    print("=" * 80)
    
    # 1. 分析GCN结构
    time_lag, station_num = analyze_gcn_structure()
    
    # 2. 分析前向传播过程
    x, adj, output, gcn = analyze_gcn_forward_process()
    
    # 3. 演示计算步骤
    x_simple, adj_simple, output_simple = demonstrate_gcn_computation_steps()
    
    # 4. 分析在模型中的使用
    analyze_gcn_usage_in_model()
    
    # 5. 创建可视化
    create_gcn_visualization()
    
    # 6. 保存详细分析
    save_detailed_analysis()
    
    print("\n" + "=" * 80)
    print("GCN分析完成！")
    print("核心发现:")
    print(f"- GCN层数: 1层")
    print(f"- 输入维度: {time_lag}")
    print(f"- 输出维度: {time_lag}")
    print(f"- 使用次数: 3次 (处理不同时间维度)")
    print(f"- 总参数: {time_lag * time_lag + time_lag} 个")
    print("\n生成文件:")
    print("- result/gcn_analysis.png (GCN分析图)")
    print("- result/gcn_implementation_analysis.txt (详细分析)")
    print("=" * 80)

if __name__ == "__main__":
    main()
