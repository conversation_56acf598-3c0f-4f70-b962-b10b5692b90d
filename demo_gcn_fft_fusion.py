"""
GCN+FFT融合模型演示脚本
展示GCN多尺度预测 + FFT高峰期预测的融合架构
"""
import torch
import numpy as np
import time
import os
import sys

# 添加当前目录到Python路径
if '.' not in sys.path:
    sys.path.append('.')

from utils.utils import GetLaplacian
from model.gcn_fft_fusion_model import GCN_FFT_FusionModel

# 可选导入数据加载器
try:
    from data.get_inflow_only_dataloader import get_inflow_only_dataloader
    DATA_LOADER_AVAILABLE = True
except ImportError:
    print("Warning: Data loader not available. Real data demo will be skipped.")
    DATA_LOADER_AVAILABLE = False

def demo_fusion_architecture():
    """演示融合架构的各个组件"""
    print("=" * 80)
    print("GCN+FFT 融合地铁客流预测系统 - 架构演示")
    print("=" * 80)
    
    # 设备配置
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 模型参数
    time_lag = 10
    pre_len = 1
    station_num = 276
    peak_time_steps = 10
    batch_size = 4
    
    print(f"\n模型配置:")
    print(f"  - 时间滞后: {time_lag}")
    print(f"  - 预测长度: {pre_len}")
    print(f"  - 车站数量: {station_num}")
    print(f"  - FFT高峰期时间步: {peak_time_steps}")
    print(f"  - 批次大小: {batch_size}")
    
    # 加载邻接矩阵
    print(f"\n正在加载邻接矩阵...")
    adjacency = np.loadtxt('./data/adjacency.csv', delimiter=",")
    adjacency = torch.tensor(GetLaplacian(adjacency).get_normalized_adj(station_num)).type(torch.float32).to(device)
    
    # 创建融合模型
    print("正在创建GCN+FFT融合模型...")
    model = GCN_FFT_FusionModel(time_lag, pre_len, station_num, device, peak_time_steps)
    model = model.to(device)
    
    # 打印模型信息
    model_info = model.get_model_info()
    print(f"\n模型信息:")
    print(f"  - 总参数量: {model_info['total_parameters']:,}")
    print(f"  - 可训练参数: {model_info['trainable_parameters']:,}")
    
    # 创建测试数据
    test_inflow = torch.randn(batch_size, station_num, time_lag * 3).to(device)
    
    print(f"\n" + "=" * 80)
    print("组件功能演示")
    print("=" * 80)
    
    with torch.no_grad():
        # 1. GCN多尺度预测演示
        print(f"\n1. GCN多尺度预测器:")
        print(f"   - 处理: 实时客流、日客流、周客流")
        gcn_output = model.gcn_predictor(test_inflow, adjacency)
        print(f"   - 输入: {test_inflow.shape}")
        print(f"   - 输出: {gcn_output.shape}")
        print(f"   - 功能: 基于图卷积网络的多时间尺度空间依赖建模")
        
        # 2. FFT高峰期预测演示
        print(f"\n2. FFT高峰期预测器:")
        print(f"   - 处理: 高峰期10个时间步的频域预测")
        peak_sequences = model.detect_peak_periods(test_inflow)
        fft_output = model.fft_predictor(peak_sequences)
        print(f"   - 高峰期数据: {peak_sequences.shape}")
        print(f"   - FFT预测输出: {fft_output.shape}")
        print(f"   - 功能: 时域→频域→MLP端到端预测→时域")
        
        # 3. 融合预测演示
        print(f"\n3. 自适应融合预测器:")
        print(f"   - 处理: GCN和FFT预测结果的智能融合")
        fusion_output = model(test_inflow, adjacency)
        print(f"   - 融合输出: {fusion_output.shape}")
        print(f"   - 功能: 基于输入特征的自适应权重融合")
        
        # 4. 预测结果分析
        print(f"\n4. 预测结果分析:")
        gcn_mean = gcn_output.mean().item()
        fft_mean = fft_output.mean().item()
        fusion_mean = fusion_output.mean().item()
        
        print(f"   - GCN预测均值: {gcn_mean:.6f}")
        print(f"   - FFT预测均值: {fft_mean:.6f}")
        print(f"   - 融合预测均值: {fusion_mean:.6f}")
        
        # 5. 计算融合权重
        input_features = test_inflow.mean(dim=1)
        adaptive_weights = model.adaptive_weight_net(input_features)
        avg_weights = adaptive_weights.mean(dim=0)
        
        print(f"\n5. 自适应融合权重:")
        print(f"   - GCN权重: {avg_weights[0].item():.3f}")
        print(f"   - FFT权重: {avg_weights[1].item():.3f}")
        print(f"   - 权重说明: 根据输入数据特征动态调整")
    
    return model, test_inflow, adjacency

def demo_training_step(model, test_inflow, adjacency):
    """演示训练步骤"""
    print(f"\n" + "=" * 80)
    print("训练步骤演示")
    print("=" * 80)
    
    # 创建优化器和损失函数
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    mse_loss = torch.nn.MSELoss()
    
    # 创建目标数据
    batch_size = test_inflow.size(0)
    target = torch.randn(batch_size, 276, 1).to(test_inflow.device)
    
    print("模拟训练步骤:")
    
    # 前向传播
    model.train()
    prediction = model(test_inflow, adjacency)
    loss = mse_loss(prediction, target)
    
    print(f"  1. 前向传播: {test_inflow.shape} -> {prediction.shape}")
    print(f"  2. 损失计算: MSE = {loss.item():.6f}")
    
    # 反向传播
    optimizer.zero_grad()
    loss.backward()
    optimizer.step()
    
    print(f"  3. 反向传播: 梯度计算和参数更新完成")
    print(f"  4. 训练步骤: 成功完成一个训练迭代")

def demo_real_data_prediction():
    """使用真实数据进行预测演示"""
    print(f"\n" + "=" * 80)
    print("真实数据预测演示")
    print("=" * 80)

    if not DATA_LOADER_AVAILABLE:
        print("数据加载器不可用，跳过真实数据演示")
        return

    try:
        # 加载真实数据
        print("正在加载真实数据...")
        _, _, test_loader, max_inflow, min_inflow = get_inflow_only_dataloader(
            time_interval=15, time_lag=10, tg_in_one_day=72, 
            forecast_day_number=5, pre_len=1, batch_size=2
        )
        
        # 设备配置
        device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
        
        # 创建模型
        model = GCN_FFT_FusionModel(10, 1, 276, device, 10)
        model = model.to(device)
        
        # 加载邻接矩阵
        adjacency = np.loadtxt('./data/adjacency.csv', delimiter=",")
        adjacency = torch.tensor(GetLaplacian(adjacency).get_normalized_adj(276)).type(torch.float32).to(device)
        
        # 使用真实数据进行预测
        model.eval()
        with torch.no_grad():
            for i, (real_X, real_Y, real_Y_orig) in enumerate(test_loader):
                if i >= 1:  # 只演示一个批次
                    break
                
                real_X = real_X.type(torch.float32).to(device)
                real_Y = real_Y.type(torch.float32).to(device)
                
                # 融合预测
                fusion_pred = model(real_X, adjacency)
                
                # 分别获取GCN和FFT预测
                gcn_pred = model.gcn_predictor(real_X, adjacency)
                peak_seq = model.detect_peak_periods(real_X)
                fft_pred = model.fft_predictor(peak_seq)
                fft_pred_adjusted = fft_pred.mean(dim=2, keepdim=True)
                
                # 反归一化
                fusion_orig = fusion_pred.cpu().numpy() * (max_inflow - min_inflow) + min_inflow
                gcn_orig = gcn_pred.cpu().numpy() * (max_inflow - min_inflow) + min_inflow
                fft_orig = fft_pred_adjusted.cpu().numpy() * (max_inflow - min_inflow) + min_inflow
                true_orig = real_Y_orig.numpy()
                
                print(f"真实数据预测结果 (前5个车站):")
                print(f"{'车站':<6} {'真实值':<8} {'GCN':<8} {'FFT':<8} {'融合':<8}")
                print("-" * 45)
                
                for station in range(5):
                    true_val = true_orig[0, station, 0]
                    gcn_val = gcn_orig[0, station, 0]
                    fft_val = fft_orig[0, station, 0]
                    fusion_val = fusion_orig[0, station, 0]
                    
                    print(f"{station:<6} {true_val:<8.1f} {gcn_val:<8.1f} {fft_val:<8.1f} {fusion_val:<8.1f}")
                
                # 计算简单指标
                mse_gcn = np.mean((gcn_orig - true_orig) ** 2)
                mse_fft = np.mean((fft_orig - true_orig) ** 2)
                mse_fusion = np.mean((fusion_orig - true_orig) ** 2)
                
                print(f"\n预测性能 (MSE):")
                print(f"  - GCN: {mse_gcn:.2f}")
                print(f"  - FFT: {mse_fft:.2f}")
                print(f"  - 融合: {mse_fusion:.2f}")
                
                break
        
        print("✓ 真实数据预测演示完成")
        
    except Exception as e:
        print(f"真实数据演示失败: {e}")
        print("请确保数据文件存在并且格式正确")

def main():
    """主演示函数"""
    print("欢迎使用 GCN+FFT 融合地铁客流预测系统!")
    print("本系统结合了GCN的多尺度空间建模和FFT的高峰期频域预测")
    
    try:
        # 架构演示
        model, test_inflow, adjacency = demo_fusion_architecture()
        
        # 训练演示
        demo_training_step(model, test_inflow, adjacency)
        
        # 真实数据演示
        demo_real_data_prediction()
        
        print(f"\n" + "=" * 80)
        print("演示完成!")
        print("=" * 80)
        print("系统特点:")
        print("  ✅ GCN: 多尺度客流预测 (实时/日/周)")
        print("  ✅ FFT: 高峰期频域预测 (10个时间步)")
        print("  ✅ 融合: 自适应权重智能融合")
        print("  ✅ 端到端: 完整的训练和预测流程")
        
        print(f"\n下一步:")
        print("  1. 运行 'python main_gcn_fft_fusion.py' 进行完整训练")
        print("  2. 运行 'python predict_gcn_fft_fusion.py' 进行预测")
        print("  3. 查看融合模型的详细性能分析")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        print("请检查:")
        print("  - 数据文件是否存在 (data/in_15min.csv, data/adjacency.csv)")
        print("  - 依赖包是否正确安装")
        print("  - 运行 'python test_fusion_simple.py' 进行诊断")

if __name__ == "__main__":
    main()
