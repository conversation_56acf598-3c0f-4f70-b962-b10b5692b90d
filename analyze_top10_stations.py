#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分析预测准确率最高的前10个车站，并绘制预测值与真实值对比图

Author: 008 - AI Algorithm Engineer for Subway Flow Prediction
"""

import numpy as np
import matplotlib.pyplot as plt
import os

# 设置matplotlib后端
import matplotlib
matplotlib.use('Agg')

def load_station_data():
    """加载车站数据"""
    print("正在加载车站数据...")
    
    # 加载车站性能指标
    metrics = np.loadtxt('result/improved_ALL_276_stations_metrics.txt', skiprows=1)
    print(f"性能指标形状: {metrics.shape}")
    
    # 加载预测值和真实值
    predictions = np.loadtxt('result/improved_ALL_276_stations_predictions.txt')
    true_values = np.loadtxt('result/improved_ALL_276_stations_original.txt')
    
    print(f"预测值形状: {predictions.shape}")
    print(f"真实值形状: {true_values.shape}")
    
    return metrics, predictions, true_values

def find_top10_stations(metrics):
    """找出预测准确率最高的前10个车站"""
    print("正在分析预测准确率最高的前10个车站...")
    
    # 性能指标列：StationID, RMSE, R2, MAE, WMAPE
    station_ids = metrics[:, 0].astype(int)
    rmse_values = metrics[:, 1]
    r2_values = metrics[:, 2]
    mae_values = metrics[:, 3]
    wmape_values = metrics[:, 4]
    
    # 使用WMAPE作为主要准确率指标（越小越好）
    # 同时考虑R2（越大越好）
    # 综合评分：(1 - WMAPE) * 0.7 + R2 * 0.3
    accuracy_scores = (1 - wmape_values) * 0.7 + r2_values * 0.3
    
    # 找出前10个最高分的车站
    top10_indices = np.argsort(accuracy_scores)[-10:][::-1]  # 降序排列
    
    top10_stations = []
    for i, idx in enumerate(top10_indices):
        station_info = {
            'rank': i + 1,
            'station_id': int(station_ids[idx]),
            'rmse': rmse_values[idx],
            'r2': r2_values[idx],
            'mae': mae_values[idx],
            'wmape': wmape_values[idx],
            'accuracy_score': accuracy_scores[idx]
        }
        top10_stations.append(station_info)
    
    return top10_stations

def plot_top10_stations_comparison(top10_stations, predictions, true_values):
    """绘制前10个车站的预测值与真实值对比图"""
    print("正在绘制前10个车站的对比图...")
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建5x2的子图布局
    fig, axes = plt.subplots(5, 2, figsize=(20, 25))
    axes = axes.flatten()
    
    # 只显示前500个数据点以便可视化
    display_points = min(500, predictions.shape[1])
    x_axis = range(display_points)
    
    for i, station_info in enumerate(top10_stations):
        station_id = station_info['station_id']
        ax = axes[i]
        
        # 获取该车站的预测值和真实值
        station_pred = predictions[station_id, :display_points]
        station_true = true_values[station_id, :display_points]
        
        # 绘制对比图
        ax.plot(x_axis, station_true, 'b-', linewidth=1.5, alpha=0.8, label='True Values')
        ax.plot(x_axis, station_pred, 'r--', linewidth=1.5, alpha=0.8, label='Predictions')
        
        # 设置标题和标签
        ax.set_title(f'Rank {station_info["rank"]}: Station {station_id}\n'
                    f'RMSE: {station_info["rmse"]:.2f}, MAE: {station_info["mae"]:.2f}, '
                    f'WMAPE: {station_info["wmape"]:.4f}',
                    fontsize=11, fontweight='bold')
        ax.set_xlabel('Time Steps', fontsize=9)
        ax.set_ylabel('Passenger Flow', fontsize=9)
        ax.legend(fontsize=8)
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 确保result目录存在
    if not os.path.exists('result'):
        os.makedirs('result')
    
    plt.savefig('result/top10_stations_prediction_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("前10个车站对比图已保存到 result/top10_stations_prediction_comparison.png")

def create_detailed_analysis(top10_stations, predictions, true_values):
    """创建详细的分析报告"""
    print("正在创建详细分析报告...")
    
    with open('result/top10_stations_analysis.txt', 'w', encoding='utf-8') as f:
        f.write("预测准确率最高的前10个车站详细分析\n")
        f.write("=" * 80 + "\n\n")
        
        f.write("评估标准:\n")
        f.write("- 综合评分 = (1 - WMAPE) × 0.7 + R² × 0.3\n")
        f.write("- WMAPE: 加权平均绝对百分比误差（越小越好）\n")
        f.write("- R²: 决定系数（越大越好）\n")
        f.write("- RMSE: 均方根误差\n")
        f.write("- MAE: 平均绝对误差\n\n")
        
        f.write("前10个车站排名:\n")
        f.write("-" * 80 + "\n")
        f.write(f"{'排名':<4} {'车站ID':<8} {'RMSE':<10} {'R²':<10} {'MAE':<10} {'WMAPE':<10} {'综合评分':<10}\n")
        f.write("-" * 80 + "\n")
        
        for station_info in top10_stations:
            f.write(f"{station_info['rank']:<4} "
                   f"{station_info['station_id']:<8} "
                   f"{station_info['rmse']:<10.4f} "
                   f"{station_info['r2']:<10.4f} "
                   f"{station_info['mae']:<10.4f} "
                   f"{station_info['wmape']:<10.4f} "
                   f"{station_info['accuracy_score']:<10.4f}\n")
        
        f.write(f"\n详细分析:\n")
        f.write("-" * 40 + "\n")
        
        for i, station_info in enumerate(top10_stations):
            station_id = station_info['station_id']
            f.write(f"\n{i+1}. 车站 {station_id} (排名第{station_info['rank']})\n")
            f.write(f"   RMSE: {station_info['rmse']:.4f}\n")
            f.write(f"   R²: {station_info['r2']:.4f}\n")
            f.write(f"   MAE: {station_info['mae']:.4f}\n")
            f.write(f"   WMAPE: {station_info['wmape']:.4f} ({station_info['wmape']*100:.2f}%)\n")
            f.write(f"   综合评分: {station_info['accuracy_score']:.4f}\n")
            
            # 计算一些统计信息
            station_pred = predictions[station_id, :]
            station_true = true_values[station_id, :]
            
            f.write(f"   预测值范围: [{station_pred.min():.2f}, {station_pred.max():.2f}]\n")
            f.write(f"   真实值范围: [{station_true.min():.2f}, {station_true.max():.2f}]\n")
            f.write(f"   平均客流: 预测={station_pred.mean():.2f}, 真实={station_true.mean():.2f}\n")
        
        f.write(f"\n总体统计:\n")
        f.write("-" * 40 + "\n")
        
        # 计算前10个车站的平均性能
        avg_rmse = np.mean([s['rmse'] for s in top10_stations])
        avg_r2 = np.mean([s['r2'] for s in top10_stations])
        avg_mae = np.mean([s['mae'] for s in top10_stations])
        avg_wmape = np.mean([s['wmape'] for s in top10_stations])
        avg_score = np.mean([s['accuracy_score'] for s in top10_stations])
        
        f.write(f"前10个车站平均性能:\n")
        f.write(f"  平均RMSE: {avg_rmse:.4f}\n")
        f.write(f"  平均R²: {avg_r2:.4f}\n")
        f.write(f"  平均MAE: {avg_mae:.4f}\n")
        f.write(f"  平均WMAPE: {avg_wmape:.4f} ({avg_wmape*100:.2f}%)\n")
        f.write(f"  平均综合评分: {avg_score:.4f}\n")
        
        # 与全体车站对比
        all_metrics = np.loadtxt('result/improved_ALL_276_stations_metrics.txt', skiprows=1)
        all_rmse = np.mean(all_metrics[:, 1])
        all_r2 = np.mean(all_metrics[:, 2])
        all_mae = np.mean(all_metrics[:, 3])
        all_wmape = np.mean(all_metrics[:, 4])
        
        f.write(f"\n与全体276个车站对比:\n")
        f.write(f"  全体平均RMSE: {all_rmse:.4f} vs 前10个: {avg_rmse:.4f}\n")
        f.write(f"  全体平均R²: {all_r2:.4f} vs 前10个: {avg_r2:.4f}\n")
        f.write(f"  全体平均MAE: {all_mae:.4f} vs 前10个: {avg_mae:.4f}\n")
        f.write(f"  全体平均WMAPE: {all_wmape:.4f} vs 前10个: {avg_wmape:.4f}\n")
        
        f.write(f"\n性能提升:\n")
        f.write(f"  RMSE提升: {((all_rmse - avg_rmse) / all_rmse * 100):+.2f}%\n")
        f.write(f"  R²提升: {((avg_r2 - all_r2) / all_r2 * 100):+.2f}%\n")
        f.write(f"  MAE提升: {((all_mae - avg_mae) / all_mae * 100):+.2f}%\n")
        f.write(f"  WMAPE提升: {((all_wmape - avg_wmape) / all_wmape * 100):+.2f}%\n")
    
    print("详细分析报告已保存到 result/top10_stations_analysis.txt")

def create_summary_table(top10_stations):
    """创建前10个车站的汇总表"""
    print("正在创建汇总表...")
    
    with open('result/top10_stations_summary.txt', 'w', encoding='utf-8') as f:
        f.write("预测准确率最高的前10个车站汇总表\n")
        f.write("=" * 60 + "\n\n")
        
        f.write(f"{'排名':<4} {'车站ID':<8} {'WMAPE':<10} {'R²':<10} {'综合评分':<10}\n")
        f.write("-" * 60 + "\n")
        
        for station_info in top10_stations:
            f.write(f"{station_info['rank']:<4} "
                   f"{station_info['station_id']:<8} "
                   f"{station_info['wmape']:<10.4f} "
                   f"{station_info['r2']:<10.4f} "
                   f"{station_info['accuracy_score']:<10.4f}\n")
    
    print("汇总表已保存到 result/top10_stations_summary.txt")

def main():
    """主函数"""
    print("=" * 80)
    print("分析预测准确率最高的前10个车站")
    print("=" * 80)
    
    # 1. 加载数据
    metrics, predictions, true_values = load_station_data()
    
    # 2. 找出前10个车站
    top10_stations = find_top10_stations(metrics)
    
    # 3. 输出前10个车站信息
    print("\n预测准确率最高的前10个车站:")
    print("-" * 80)
    print(f"{'排名':<4} {'车站ID':<8} {'RMSE':<10} {'R²':<10} {'MAE':<10} {'WMAPE':<10}")
    print("-" * 80)
    
    for station_info in top10_stations:
        print(f"{station_info['rank']:<4} "
              f"{station_info['station_id']:<8} "
              f"{station_info['rmse']:<10.4f} "
              f"{station_info['r2']:<10.4f} "
              f"{station_info['mae']:<10.4f} "
              f"{station_info['wmape']:<10.4f}")
    
    # 4. 绘制对比图
    plot_top10_stations_comparison(top10_stations, predictions, true_values)
    
    # 5. 创建详细分析
    create_detailed_analysis(top10_stations, predictions, true_values)
    
    # 6. 创建汇总表
    create_summary_table(top10_stations)
    
    print("\n" + "=" * 80)
    print("分析完成！")
    print("生成的文件:")
    print("- result/top10_stations_prediction_comparison.png (对比图)")
    print("- result/top10_stations_analysis.txt (详细分析)")
    print("- result/top10_stations_summary.txt (汇总表)")
    print("=" * 80)

if __name__ == "__main__":
    main()
